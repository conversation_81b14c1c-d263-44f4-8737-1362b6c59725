package cn.need.cloud.biz.service.binlocation.impl;

import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.framework.common.core.exception.BusinessException;
import cn.need.framework.common.core.util.ErrorMessages;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * BinLocationDetailLockedServiceImpl 死锁修复版本
 * 
 * 修复要点：
 * 1. 按ID排序避免锁定顺序不一致
 * 2. 减小批量更新大小
 * 3. 添加重试机制
 * 4. 优化事务粒度
 */
@Slf4j
public class BinLocationDetailLockedServiceImplDeadlockFix {

    /**
     * 修复版本：通过排序和分批处理避免死锁
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByChange(Collection<? extends BinLocationDetailChangeBO> changeList) {
        Validate.notEmpty(changeList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Location lock"));

        Set<BinLocationDetailLocked> lockedList = new HashSet<>();
        for (BinLocationDetailChangeBO move : changeList) {
            Validate.notNull(move.getSourceLock(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "source bin location detail locked"));
            Validate.notNull(move.getDestLock(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "destination bin location detail locked"));
            
            BinLocationDetailLocked locked = move.getSourceLock();
            int finishQty = move.moveLock();

            Validate.isTrue(finishQty <= locked.getQty(),
                    "{}, The released quantity cannot exceed the locked quantity, {} > {}",
                    move.toLockLog(), finishQty, locked.getQty()
            );
            
            locked.setLockedStatus(finishQty == locked.getQty()
                    ? BinLocationDetailLockedStatusEnum.RELEASE.getStatus()
                    : BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus());

            lockedList.add(move.getSourceLock());
            lockedList.add(move.getDestLock());
        }
        
        // 修复：使用优化的批量更新方法
        updateBatchWithDeadlockPrevention(lockedList);
    }

    /**
     * 防死锁的批量更新方法
     * 
     * @param lockedList 需要更新的锁定记录列表
     */
    private void updateBatchWithDeadlockPrevention(Set<BinLocationDetailLocked> lockedList) {
        if (lockedList.isEmpty()) {
            return;
        }

        // 1. 按ID排序，确保锁定顺序一致
        List<BinLocationDetailLocked> sortedList = lockedList.stream()
                .sorted(Comparator.comparing(BinLocationDetailLocked::getId))
                .collect(Collectors.toList());

        // 2. 分批处理，减少锁竞争
        int batchSize = 50; // 可配置的批量大小
        List<List<BinLocationDetailLocked>> batches = partitionList(sortedList, batchSize);

        // 3. 逐批更新，带重试机制
        int totalUpdated = 0;
        for (int i = 0; i < batches.size(); i++) {
            List<BinLocationDetailLocked> batch = batches.get(i);
            int updated = updateBatchWithRetry(batch, i + 1, batches.size());
            totalUpdated += updated;
        }

        // 4. 验证更新结果
        Validate.isTrue(totalUpdated == lockedList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, 
                        "Failed to update bin location detail locked quantity: expected %d, actual %d", 
                        lockedList.size(), totalUpdated)
        );
    }

    /**
     * 带重试机制的批量更新
     * 
     * @param batch 批次数据
     * @param batchNum 批次号
     * @param totalBatches 总批次数
     * @return 更新的记录数
     */
    private int updateBatchWithRetry(List<BinLocationDetailLocked> batch, int batchNum, int totalBatches) {
        int maxRetries = 3;
        int retryDelay = 100; // 毫秒
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.debug("Updating batch {}/{}, size: {}, attempt: {}", 
                         batchNum, totalBatches, batch.size(), attempt);
                
                int updated = super.updateBatch(batch);
                
                if (updated != batch.size()) {
                    log.warn("Batch update incomplete: expected {}, actual {}", batch.size(), updated);
                }
                
                return updated;
                
            } catch (Exception e) {
                if (isDeadlockException(e) && attempt < maxRetries) {
                    log.warn("Deadlock detected in batch {}/{}, attempt {}/{}, retrying in {}ms", 
                            batchNum, totalBatches, attempt, maxRetries, retryDelay);
                    
                    try {
                        Thread.sleep(retryDelay * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BusinessException("Update interrupted", ie);
                    }
                } else {
                    log.error("Failed to update batch {}/{} after {} attempts", 
                             batchNum, totalBatches, attempt, e);
                    throw new BusinessException("Failed to update bin location detail locked", e);
                }
            }
        }
        
        throw new BusinessException(String.format(
                "Failed to update batch %d/%d after %d attempts due to persistent deadlocks", 
                batchNum, totalBatches, maxRetries));
    }

    /**
     * 判断是否为死锁异常
     */
    private boolean isDeadlockException(Exception e) {
        String message = e.getMessage();
        return message != null && (
                message.contains("Deadlock found when trying to get lock") ||
                message.contains("deadlock") ||
                message.contains("Lock wait timeout exceeded")
        );
    }

    /**
     * 将列表分割为指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }
}

/**
 * 配置类：死锁预防参数
 */
@ConfigurationProperties(prefix = "app.deadlock-prevention")
@Data
public static class DeadlockPreventionConfig {
    
    /**
     * 批量更新大小
     */
    private int batchSize = 50;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 重试基础延迟（毫秒）
     */
    private int retryBaseDelay = 100;
    
    /**
     * 是否启用死锁预防
     */
    private boolean enabled = true;
}
