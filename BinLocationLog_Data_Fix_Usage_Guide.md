# BinLocationLog 数据修复使用指南

## 概述

我已经为您创建了完整的数据修复解决方案，包括：
1. **SQL 脚本**：直接在数据库层面修复
2. **Java 服务**：通过应用程序修复
3. **REST API**：提供便捷的修复接口

## 🚀 快速开始

### 方式一：使用 REST API（推荐）

#### 1. 分析数据问题
```bash
GET /binlocationlog/datafix/analyze?startTime=2024-01-01 00:00:00
```

**响应示例**：
```json
{
  "totalRecords": 1000,
  "brokenChainCount": 150,
  "mathErrorCount": 50,
  "duplicateDestCount": 200,
  "brokenChainPercentage": 15.0
}
```

#### 2. 执行完整修复（一键修复）
```bash
POST /binlocationlog/datafix/fix/full?startTime=2024-01-01 00:00:00
```

**响应示例**：
```json
{
  "success": true,
  "analysisResult": {
    "totalRecords": 1000,
    "brokenChainCount": 150,
    "mathErrorCount": 50,
    "duplicateDestCount": 200,
    "brokenChainPercentage": 15.0
  },
  "inventoryAuditFixCount": 50,
  "chainFixCount": 100,
  "duplicateCleanCount": 200,
  "validationResult": {
    "totalRecords": 1000,
    "brokenChainCount": 0,
    "mathErrorCount": 0,
    "duplicateDestCount": 0,
    "chainContinuityValid": true,
    "mathRelationValid": true,
    "duplicateCleanValid": true,
    "overallValid": true
  }
}
```

#### 3. 分步骤修复（更安全）
```bash
POST /binlocationlog/datafix/fix/step-by-step?startTime=2024-01-01 00:00:00
```

### 方式二：使用 SQL 脚本

#### 1. 执行备份
```sql
-- 创建备份表
CREATE TABLE binlocationlog_backup_20250704 AS 
SELECT * FROM binlocationlog 
WHERE create_time >= '2024-01-01';
```

#### 2. 执行修复脚本
运行 `BinLocationLogDataFix.sql` 中的脚本

#### 3. 验证结果
```sql
-- 验证链条连续性
WITH log_chain AS (
    SELECT 
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as prev_after_qty
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
)
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) as broken_chain_count
FROM log_chain;
```

## 📋 详细步骤说明

### 步骤 1：数据分析
- **目的**：了解当前数据问题的规模
- **API**：`GET /binlocationlog/datafix/analyze`
- **关键指标**：
  - `brokenChainCount`：链条断裂的记录数
  - `mathErrorCount`：数学关系错误的记录数
  - `duplicateDestCount`：重复 dest 记录数

### 步骤 2：修复 inventoryaudit
- **目的**：修复盘点数据的 beforeqty
- **API**：`POST /binlocationlog/datafix/fix/inventory-audit`
- **原理**：`beforeqty = afterqty - changeqty`
- **适用**：inventoryaudit 类型的记录

### 步骤 3：重建链条连续性
- **目的**：确保每条记录的 beforeqty = 前一条记录的 afterqty
- **API**：`POST /binlocationlog/datafix/fix/chain-continuity`
- **原理**：按时间顺序重建链条
- **适用**：SHIP、TRANSFER、PICK、PUT_AWAY 类型

### 步骤 4：清理重复记录
- **目的**：将同一库位操作的 dest 字段设为 null
- **API**：`POST /binlocationlog/datafix/fix/clean-duplicates`
- **原理**：`source_bin_location_id = dest_bin_location_id` 时清理 dest

### 步骤 5：验证结果
- **目的**：确认修复效果
- **API**：`GET /binlocationlog/datafix/validate`
- **成功标准**：
  - 链条断裂率 < 1%
  - 数学关系错误率 = 0%
  - 重复记录数 = 0

## 🛡️ 安全建议

### 1. 执行前准备
```bash
# 1. 备份数据库
mysqldump -u username -p database_name binlocationlog > binlocationlog_backup.sql

# 2. 在测试环境先执行
# 3. 选择业务低峰期执行
# 4. 准备回滚方案
```

### 2. 分批执行
如果数据量很大（>10万条），建议分批执行：

```bash
# 按月份分批修复
POST /binlocationlog/datafix/fix/full?startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59
POST /binlocationlog/datafix/fix/full?startTime=2024-02-01 00:00:00&endTime=2024-02-29 23:59:59
# ...
```

### 3. 监控执行过程
```bash
# 实时监控修复进度
tail -f logs/application.log | grep "BinLocationLogDataFix"
```

## 📊 修复效果验证

### 验证链条连续性
```sql
-- 检查链条断裂情况
WITH log_chain AS (
    SELECT 
        id,
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        create_time,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as prev_after_qty
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    AND create_time >= '2024-01-01'
)
SELECT 
    source_bin_location_id,
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) as broken_count
FROM log_chain
GROUP BY source_bin_location_id
HAVING broken_count > 0
ORDER BY broken_count DESC;
```

### 验证数学关系
```sql
-- 检查 beforeqty + changeqty = afterqty
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE 
        WHEN (source_before_in_stock_qty + source_change_in_stock_qty) != source_after_in_stock_qty 
        THEN 1 
    END) as math_error_count
FROM binlocationlog 
WHERE source_bin_location_id IS NOT NULL
AND create_time >= '2024-01-01';
```

### 验证重复清理
```sql
-- 检查同一库位操作的 dest 字段清理
SELECT 
    COUNT(*) as same_location_ops,
    COUNT(CASE WHEN dest_bin_location_id IS NOT NULL THEN 1 END) as still_has_dest
FROM binlocationlog 
WHERE source_bin_location_id IS NOT NULL
AND create_time >= '2024-01-01';
```

## 🔧 故障排除

### 问题 1：修复后仍有链条断裂
**原因**：第一条记录的 beforeqty 可能不正确
**解决**：手动检查并修复第一条记录

### 问题 2：数学关系仍然错误
**原因**：源数据的 afterqty 或 changeqty 可能有问题
**解决**：检查源数据，必要时手动修正

### 问题 3：修复过程中出现异常
**原因**：数据约束或并发问题
**解决**：检查日志，分批执行，或使用 SQL 脚本

## 📞 联系支持

如果在修复过程中遇到问题：
1. 查看应用日志：`logs/application.log`
2. 检查数据库日志
3. 联系技术支持团队

## 🎯 修复完成检查清单

- [ ] 数据已备份
- [ ] 修复脚本已执行
- [ ] 链条连续性验证通过（断裂率 < 1%）
- [ ] 数学关系验证通过（错误率 = 0%）
- [ ] 重复记录清理完成
- [ ] 业务功能测试通过
- [ ] 性能测试通过

修复完成后，您的 `binlocationlog` 数据将形成正确的连续链条，准确反映库存变化轨迹！
