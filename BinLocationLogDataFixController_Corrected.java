package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.service.log.BinLocationLogDataFixService;
import cn.need.framework.common.core.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * BinLocationLog 数据修复控制器（修正版）
 * 
 * 提供数据修复的 REST API 接口
 */
@Slf4j
@RestController
@RequestMapping("/api/binlocation-log/fix")
@RequiredArgsConstructor
@Tag(name = "BinLocationLog数据修复", description = "修复链条连续性和数据准确性问题")
public class BinLocationLogDataFixController {

    private final BinLocationLogDataFixService dataFixService;

    /**
     * 分析数据问题
     */
    @GetMapping("/analyze")
    @Operation(summary = "分析数据问题", description = "分析指定时间后的BinLocationLog数据问题")
    public Result<BinLocationLogDataFixService.DataAnalysisResult> analyzeDataProblems(
            @Parameter(description = "起始时间", example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime) {
        
        log.info("开始分析数据问题，起始时间: {}", startTime);
        
        try {
            BinLocationLogDataFixService.DataAnalysisResult result = dataFixService.analyzeDataProblems(startTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分析数据问题失败", e);
            return Result.error("分析失败: " + e.getMessage());
        }
    }

    /**
     * 修复 inventoryaudit 数据
     */
    @PostMapping("/fix-audit")
    @Operation(summary = "修复inventoryaudit数据", description = "修复inventoryaudit操作的beforeqty数据")
    public Result<Integer> fixInventoryAuditData(
            @Parameter(description = "起始时间", example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime) {
        
        log.info("开始修复 inventoryaudit 数据，起始时间: {}", startTime);
        
        try {
            int fixCount = dataFixService.fixInventoryAuditData(startTime);
            log.info("inventoryaudit 数据修复完成，修复记录数: {}", fixCount);
            return Result.success(fixCount);
        } catch (Exception e) {
            log.error("修复 inventoryaudit 数据失败", e);
            return Result.error("修复失败: " + e.getMessage());
        }
    }

    /**
     * 重建链条连续性
     */
    @PostMapping("/rebuild-chain")
    @Operation(summary = "重建链条连续性", description = "重建BinLocationLog记录的链条连续性")
    public Result<Integer> rebuildChainContinuity(
            @Parameter(description = "起始时间", example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime) {
        
        log.info("开始重建链条连续性，起始时间: {}", startTime);
        
        try {
            int fixCount = dataFixService.rebuildChainContinuity(startTime);
            log.info("链条连续性重建完成，修复记录数: {}", fixCount);
            return Result.success(fixCount);
        } catch (Exception e) {
            log.error("重建链条连续性失败", e);
            return Result.error("重建失败: " + e.getMessage());
        }
    }

    /**
     * 执行完整的数据修复
     */
    @PostMapping("/full-fix")
    @Operation(summary = "执行完整数据修复", description = "执行完整的数据修复流程，包括audit修复和链条重建")
    public Result<BinLocationLogDataFixService.DataFixResult> executeFullDataFix(
            @Parameter(description = "起始时间", example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime) {
        
        log.info("开始执行完整数据修复，起始时间: {}", startTime);
        
        try {
            BinLocationLogDataFixService.DataFixResult result = dataFixService.executeFullDataFix(startTime);
            
            if (result.isSuccess()) {
                log.info("完整数据修复成功完成");
                return Result.success(result);
            } else {
                log.error("完整数据修复失败: {}", result.getErrorMessage());
                return Result.error("修复失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("执行完整数据修复失败", e);
            return Result.error("修复失败: " + e.getMessage());
        }
    }

    /**
     * 验证修复结果
     */
    @GetMapping("/validate")
    @Operation(summary = "验证修复结果", description = "验证数据修复后的结果")
    public Result<BinLocationLogDataFixService.DataAnalysisResult> validateFixResult(
            @Parameter(description = "起始时间", example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime) {
        
        log.info("开始验证修复结果，起始时间: {}", startTime);
        
        try {
            BinLocationLogDataFixService.DataAnalysisResult result = dataFixService.analyzeDataProblems(startTime);
            
            // 判断修复是否成功
            boolean isFixed = result.getBrokenChainCount() == 0 && result.getMathErrorCount() == 0;
            
            if (isFixed) {
                log.info("验证通过：数据修复成功，无链条断裂和数学错误");
            } else {
                log.warn("验证发现问题：链条断裂={}, 数学错误={}", 
                        result.getBrokenChainCount(), result.getMathErrorCount());
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证修复结果失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }
}
