package cn.need.cloud.biz.service.base.impl;

import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.framework.common.core.util.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * PickingSlipServiceImpl 事务优化版本
 * 
 * 优化要点：
 * 1. 拆分大事务为小事务
 * 2. 按库位分组处理，减少锁竞争
 * 3. 优化锁定顺序
 */
@Slf4j
public class PickingSlipServiceImplTransactionOptimization {

    /**
     * 优化版本：拆分事务，减少死锁风险
     */
    @Override
    public void releaseLockAndReduceInStock(Collection<BinLocationDetailLockedChangeBO> changeList) {
        if (changeList.isEmpty()) {
            return;
        }

        // 设置变更库位信息
        List<BinLocationDetailLocked> lockedList = StreamUtils.distinctMap(changeList, BinLocationDetailLockedChangeBO::getSourceLock);
        List<Long> detailIdList = StreamUtils.distinctMap(lockedList, BinLocationDetailLocked::getBinLocationDetailId);
        Map<Long, BinLocationDetail> binLocationDetailMap = StreamUtils.toMap(binLocationDetailService.listByIds(detailIdList), IdModel::getId);
        
        changeList.forEach(change -> {
            BinLocationDetail source = binLocationDetailMap.get(change.getSourceLock().getBinLocationDetailId());
            change.setSource(source);
            change.setDest(source);
        });

        // 按库位分组，减少锁竞争
        Map<Long, List<BinLocationDetailChangeBO>> changesByBinLocation = changeList.stream()
                .collect(Collectors.groupingBy(
                        change -> change.getSource().getBinLocationId(),
                        LinkedHashMap::new, // 保持顺序
                        Collectors.toList()
                ));

        // 按库位ID排序，确保处理顺序一致
        List<Map.Entry<Long, List<BinLocationDetailChangeBO>>> sortedEntries = changesByBinLocation.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toList());

        // 分库位处理，每个库位一个独立事务
        for (Map.Entry<Long, List<BinLocationDetailChangeBO>> entry : sortedEntries) {
            Long binLocationId = entry.getKey();
            List<BinLocationDetailChangeBO> binLocationChanges = entry.getValue();
            
            try {
                processBinLocationChanges(binLocationId, binLocationChanges);
            } catch (Exception e) {
                log.error("Failed to process changes for bin location {}: {}", binLocationId, e.getMessage(), e);
                throw new BusinessException(String.format("Failed to process inventory changes for bin location %d", binLocationId), e);
            }
        }
    }

    /**
     * 处理单个库位的变更（独立事务）
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processBinLocationChanges(Long binLocationId, List<BinLocationDetailChangeBO> changeList) {
        log.debug("Processing {} changes for bin location {}", changeList.size(), binLocationId);

        // 1. 先处理锁释放
        releaseLockForBinLocation(changeList);

        // 2. 再处理库存更新
        updateInventoryForBinLocation(changeList);

        log.debug("Successfully processed {} changes for bin location {}", changeList.size(), binLocationId);
    }

    /**
     * 释放单个库位的锁（按产品分组处理）
     */
    private void releaseLockForBinLocation(List<BinLocationDetailChangeBO> changeList) {
        // 按产品分组，进一步减少锁竞争
        Map<Long, List<BinLocationDetailChangeBO>> changesByProduct = changeList.stream()
                .collect(Collectors.groupingBy(
                        change -> change.getSource().getProductId(),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        // 按产品ID排序处理
        changesByProduct.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    Long productId = entry.getKey();
                    List<BinLocationDetailChangeBO> productChanges = entry.getValue();
                    
                    // 按锁ID排序，确保锁定顺序一致
                    List<BinLocationDetailChangeBO> sortedChanges = productChanges.stream()
                            .sorted(Comparator.comparing(change -> change.getSourceLock().getId()))
                            .collect(Collectors.toList());

                    try {
                        binLocationDetailLockedService.updateByChange(sortedChanges);
                        log.debug("Released {} locks for product {} in bin location", 
                                 sortedChanges.size(), productId);
                    } catch (Exception e) {
                        log.error("Failed to release locks for product {}: {}", productId, e.getMessage());
                        throw e;
                    }
                });
    }

    /**
     * 更新单个库位的库存
     */
    private void updateInventoryForBinLocation(List<BinLocationDetailChangeBO> changeList) {
        // 合并同一库位详情的变更
        Map<String, BinLocationDetailChangeBO> mergedChanges = new HashMap<>();
        
        for (BinLocationDetailChangeBO change : changeList) {
            String key = change.getSource().getId() + ":" + change.getDest().getId();
            
            if (mergedChanges.containsKey(key)) {
                BinLocationDetailChangeBO existing = mergedChanges.get(key);
                existing.setChangeQty(existing.getChangeQty() + change.getChangeQty());
            } else {
                mergedChanges.put(key, change);
            }
        }

        List<BinLocationDetailChangeBO> fixChangeList = new ArrayList<>(mergedChanges.values());
        
        // 按库位详情ID排序
        fixChangeList.sort(Comparator.comparing(change -> change.getSource().getId()));

        try {
            binLocationDetailService.updateInStockByChange(fixChangeList);
            log.debug("Updated inventory for {} bin location details", fixChangeList.size());
        } catch (Exception e) {
            log.error("Failed to update inventory: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 批量处理版本：当单个库位变更较多时使用
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processBinLocationChangesBatch(Long binLocationId, List<BinLocationDetailChangeBO> changeList) {
        if (changeList.size() <= 20) {
            // 小批量直接处理
            processBinLocationChanges(binLocationId, changeList);
            return;
        }

        // 大批量分批处理
        int batchSize = 20;
        List<List<BinLocationDetailChangeBO>> batches = partitionList(changeList, batchSize);
        
        for (int i = 0; i < batches.size(); i++) {
            List<BinLocationDetailChangeBO> batch = batches.get(i);
            log.debug("Processing batch {}/{} for bin location {}, size: {}", 
                     i + 1, batches.size(), binLocationId, batch.size());
            
            processBinLocationChanges(binLocationId, batch);
        }
    }

    /**
     * 分割列表为批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }
}

/**
 * 死锁监控和统计
 */
@Component
@Slf4j
public class DeadlockMonitor {
    
    private final AtomicLong deadlockCount = new AtomicLong(0);
    private final AtomicLong retryCount = new AtomicLong(0);
    
    public void recordDeadlock(String operation, Exception e) {
        deadlockCount.incrementAndGet();
        log.warn("Deadlock detected in operation: {}, total deadlocks: {}, error: {}", 
                operation, deadlockCount.get(), e.getMessage());
    }
    
    public void recordRetry(String operation) {
        retryCount.incrementAndGet();
        log.debug("Retry executed for operation: {}, total retries: {}", 
                operation, retryCount.get());
    }
    
    @EventListener
    @Async
    public void handleDeadlockEvent(DeadlockEvent event) {
        // 发送告警、记录指标等
        log.error("Deadlock event: operation={}, thread={}, details={}", 
                 event.getOperation(), event.getThreadName(), event.getDetails());
    }
}
