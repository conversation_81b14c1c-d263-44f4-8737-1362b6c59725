package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.param.product.update.ProductComponentCreateOrUpdateParam;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductProgramVO;
import cn.need.cloud.biz.service.product.impl.ProductComponentServiceImpl;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 产品组件服务实现类的单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ProductComponentServiceImplTest {

    @Mock
    private ProductService productService;

    @InjectMocks
    private ProductComponentServiceImpl productComponentService;

    // 测试数据
    private static final Long ASSEMBLY_PRODUCT_ID = 1001L;
    private static final Long COMPONENT_PRODUCT_ID_1 = 2001L;
    private static final Long COMPONENT_PRODUCT_ID_2 = 2002L;
    private static final Long TRANSACTION_PARTNER_ID = 3001L;

    private List<ProductComponentCreateOrUpdateParam> validParamList;
    private BaseProductVO mockProduct;

    @BeforeEach
    public void setUp() {
        // 设置有效的参数列表
        validParamList = new ArrayList<>();
        validParamList.add(new ProductComponentCreateOrUpdateParam(ASSEMBLY_PRODUCT_ID, COMPONENT_PRODUCT_ID_1, "Assembly instruction 1", 2));
        validParamList.add(new ProductComponentCreateOrUpdateParam(ASSEMBLY_PRODUCT_ID, COMPONENT_PRODUCT_ID_2, "Assembly instruction 2", 3));

        // 创建模拟产品对象
        mockProduct = new BaseProductVO();
        mockProduct.setTransactionPartnerId(TRANSACTION_PARTNER_ID);
    }

    @Test
    @DisplayName("测试空参数列表")
    public void testCreateOrUpdateWithEmptyParamList() {
        // 测试空参数列表
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            productComponentService.createOrUpdate(Collections.emptyList());
        });

        // 验证抛出的异常消息
        assertTrue(exception.getMessage().contains("Parameter cannot be empty"));
    }

    @Test
    @DisplayName("测试不同交易伙伴的产品")
    public void testCreateOrUpdateWithDifferentPartners() {
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 false
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(false);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                productComponentService.createOrUpdate(validParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("Component products must belong to the same transaction partner"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }

    @Test
    @DisplayName("测试组产品作为组装产品")
    public void testCreateOrUpdateWithGroupAsAssembly() {
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 true
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(true);
            
            // 模拟 productService.isGroup 返回 true
            when(productService.isGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(true);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                productComponentService.createOrUpdate(validParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("Group product"));
            assertTrue(exception.getMessage().contains("cannot be updated with components"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }

    @Test
    @DisplayName("测试多箱产品作为组装产品")
    public void testCreateOrUpdateWithMultiboxAsAssembly() {
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 true
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(true);
            
            // 模拟 productService.isGroup 返回 false
            when(productService.isGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            
            // 模拟 productService.isMultibox 返回 true
            when(productService.isMultibox(ASSEMBLY_PRODUCT_ID)).thenReturn(true);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                productComponentService.createOrUpdate(validParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("MultiBox product"));
            assertTrue(exception.getMessage().contains("cannot be updated with components"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }

    @Test
    @DisplayName("测试组件产品作为组装产品")
    public void testCreateOrUpdateWithComponentAsAssembly() {
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 true
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(true);
            
            // 模拟 productService.isGroup 返回 false
            when(productService.isGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            
            // 模拟 productService.isMultibox 返回 false
            when(productService.isMultibox(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            
            // 模拟 isComponent 返回 true
            ProductComponentServiceImpl spyService = spy(productComponentService);
            doReturn(true).when(spyService).isComponent(ASSEMBLY_PRODUCT_ID);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                spyService.createOrUpdate(validParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("Component product"));
            assertTrue(exception.getMessage().contains("cannot be used as assembly"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }
    
    @Test
    @DisplayName("测试子产品组作为组件")
    public void testCreateOrUpdateWithChildGroupAsComponent() {
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 true
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(true);
            
            // 模拟验证通过
            when(productService.isGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            when(productService.isMultibox(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            
            ProductComponentServiceImpl spyService = spy(productComponentService);
            doReturn(false).when(spyService).isComponent(ASSEMBLY_PRODUCT_ID);
            
            // 模拟 productService.isChildGroup 返回 true 表示组件是子产品组
            when(productService.isChildGroup(COMPONENT_PRODUCT_ID_1)).thenReturn(true);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                spyService.createOrUpdate(validParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("Cannot add child product group"));
            assertTrue(exception.getMessage().contains("as component"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }
    
    @Test
    @DisplayName("测试产品自引用")
    public void testCreateOrUpdateWithSelfReference() {
        // 创建自引用参数列表
        List<ProductComponentCreateOrUpdateParam> selfRefParamList = new ArrayList<>();
        selfRefParamList.add(new ProductComponentCreateOrUpdateParam(ASSEMBLY_PRODUCT_ID, ASSEMBLY_PRODUCT_ID, "Self reference", 1));
        
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 true
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(true);
            
            // 模拟验证通过
            when(productService.isGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            when(productService.isMultibox(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            
            ProductComponentServiceImpl spyService = spy(productComponentService);
            doReturn(false).when(spyService).isComponent(ASSEMBLY_PRODUCT_ID);
            
            // 模拟 productService.isChildGroup 返回 false
            when(productService.isChildGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(false);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                spyService.createOrUpdate(selfRefParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("Cannot add product"));
            assertTrue(exception.getMessage().contains("as its own component"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }

    @Test
    @DisplayName("测试组装产品作为组件")
    public void testCreateOrUpdateWithAssemblyAsComponent() {
        try (MockedStatic<ProductCacheUtil> mockedStatic = Mockito.mockStatic(ProductCacheUtil.class)) {
            // 设置 ProductCacheUtil.getById 的行为
            mockedStatic.when(() -> ProductCacheUtil.getById(anyLong())).thenReturn(mockProduct);

            // 模拟 productService.isAllProductInPartnerId 返回 true
            when(productService.isAllProductInPartnerId(eq(TRANSACTION_PARTNER_ID), anyList())).thenReturn(true);
            
            // 模拟验证通过
            when(productService.isGroup(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            when(productService.isMultibox(ASSEMBLY_PRODUCT_ID)).thenReturn(false);
            
            ProductComponentServiceImpl spyService = spy(productComponentService);
            doReturn(false).when(spyService).isComponent(ASSEMBLY_PRODUCT_ID);
            
            // 模拟 productService.isChildGroup 返回 false
            when(productService.isChildGroup(anyLong())).thenReturn(false);
            
            // 模拟 productService.isAssembly 返回 true 表示组件是组装产品
            when(productService.isAssembly(COMPONENT_PRODUCT_ID_1)).thenReturn(true);

            // 执行测试
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                spyService.createOrUpdate(validParamList);
            });

            // 验证异常信息包含产品信息
            assertTrue(exception.getMessage().contains("Assembly product"));
            assertTrue(exception.getMessage().contains("cannot be used as component"));
            assertTrue(exception.getMessage().contains("Product:"));
        }
    }
} 