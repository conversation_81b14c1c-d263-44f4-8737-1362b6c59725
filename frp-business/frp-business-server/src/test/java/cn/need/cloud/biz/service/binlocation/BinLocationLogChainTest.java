// package cn.need.cloud.biz.service.binlocation;
//
// import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
// import cn.need.cloud.biz.model.bo.ref.RefTableBO;
// import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
// import cn.need.cloud.biz.model.entity.log.BinLocationLog;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
//
// import java.util.ArrayList;
// import java.util.List;
//
// import static org.assertj.core.api.Assertions.assertThat;
//
// /**
//  * BinLocationLog 链条连续性测试
//  *
//  * 测试目标：确保 binlocationlog 记录形成正确的链条，
//  * 每条记录的 afterQty 应该等于下一条记录的 beforeQty
//  */
// @SpringBootTest
// @DisplayName("BinLocationLog 链条连续性测试")
// class BinLocationLogChainTest {
//
//     @Test
//     @DisplayName("测试单次操作的日志记录正确性")
//     void testSingleOperationLogCorrectness() {
//         // 准备测试数据
//         BinLocationDetail binLocation = new BinLocationDetail();
//         binLocation.setId(1L);
//         binLocation.setBinLocationId(100L);
//         binLocation.setProductId(200L);
//         binLocation.setProductVersionId(300L);
//         binLocation.setInStockQty(50);
//
//         RefTableBO refTable = new RefTableBO();
//         refTable.setRefTableId(1L);
//         refTable.setRefTableName("TestTable");
//         refTable.setRefTableRefNum("TEST001");
//
//         // 创建变更对象
//         BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
//         changeBO.setSource(binLocation);
//         changeBO.setDest(binLocation); // 同一库位
//         changeBO.setChangeQty(10);
//         changeBO.setChangeType("SHIP");
//         changeBO.setRefTable(refTable);
//
//         // 执行移动操作（这会记录移动前的数量）
//         changeBO.move();
//
//         // 执行测试
//         BinLocationLog log = changeBO.toBinLocationLog();
//
//         // 验证结果
//         assertThat(log).isNotNull();
//         assertThat(log.getSourceBinLocationId()).isEqualTo(100L);
//         assertThat(log.getSourceBinLocationDetailId()).isEqualTo(1L);
//         assertThat(log.getSourceChangeInStockQty()).isEqualTo(-10);
//
//         // 验证链条连续性：beforeQty应该是真实记录的移动前数量
//         assertThat(log.getSourceBeforeInStockQty()).isEqualTo(50); // 移动前的真实数量
//         assertThat(log.getSourceAfterInStockQty()).isEqualTo(40);  // 移动后的数量
//
//         // 验证同一库位操作时dest字段记录相同的库位信息（这是正确的业务逻辑）
//         assertThat(log.getDestBinLocationId()).isEqualTo(100L);
//         assertThat(log.getDestBinLocationDetailId()).isEqualTo(1L);
//         assertThat(log.getDestChangeInStockQty()).isEqualTo(10);
//         assertThat(log.getDestAfterInStockQty()).isEqualTo(40);
//         assertThat(log.getDestBeforeInStockQty()).isEqualTo(50);
//     }
//
//     @Test
//     @DisplayName("测试多次操作的日志链条连续性")
//     void testMultipleOperationsChainContinuity() {
//         // 准备测试数据 - 模拟同一库位的连续操作
//         BinLocationDetail binLocation = new BinLocationDetail();
//         binLocation.setId(1L);
//         binLocation.setBinLocationId(100L);
//         binLocation.setProductId(200L);
//         binLocation.setProductVersionId(300L);
//         binLocation.setInStockQty(100); // 初始库存100
//
//         RefTableBO refTable = new RefTableBO();
//         refTable.setRefTableId(1L);
//         refTable.setRefTableName("TestTable");
//         refTable.setRefTableRefNum("TEST001");
//
//         List<BinLocationLog> logChain = new ArrayList<>();
//
//         // 第一次操作：扣减20
//         BinLocationDetailChangeBO change1 = new BinLocationDetailChangeBO();
//         change1.setSource(binLocation);
//         change1.setDest(binLocation);
//         change1.setChangeQty(20);
//         change1.setChangeType("SHIP");
//         change1.setRefTable(refTable);
//
//         change1.move(); // 库存变为80
//         BinLocationLog log1 = change1.toBinLocationLog();
//         logChain.add(log1);
//
//         // 第二次操作：扣减15
//         BinLocationDetailChangeBO change2 = new BinLocationDetailChangeBO();
//         change2.setSource(binLocation);
//         change2.setDest(binLocation);
//         change2.setChangeQty(15);
//         change2.setChangeType("SHIP");
//         change2.setRefTable(refTable);
//
//         change2.move(); // 库存变为65
//         BinLocationLog log2 = change2.toBinLocationLog();
//         logChain.add(log2);
//
//         // 第三次操作：扣减10
//         BinLocationDetailChangeBO change3 = new BinLocationDetailChangeBO();
//         change3.setSource(binLocation);
//         change3.setDest(binLocation);
//         change3.setChangeQty(10);
//         change3.setChangeType("SHIP");
//         change3.setRefTable(refTable);
//
//         change3.move(); // 库存变为55
//         BinLocationLog log3 = change3.toBinLocationLog();
//         logChain.add(log3);
//
//         // 验证链条连续性
//         // 第一次操作：100 -> 80
//         assertThat(log1.getSourceBeforeInStockQty()).isEqualTo(100);
//         assertThat(log1.getSourceAfterInStockQty()).isEqualTo(80);
//
//         // 第二次操作：80 -> 65，beforeQty应该等于上一次的afterQty
//         assertThat(log2.getSourceBeforeInStockQty()).isEqualTo(80);
//         assertThat(log2.getSourceAfterInStockQty()).isEqualTo(65);
//         assertThat(log2.getSourceBeforeInStockQty()).isEqualTo(log1.getSourceAfterInStockQty());
//
//         // 第三次操作：65 -> 55，beforeQty应该等于上一次的afterQty
//         assertThat(log3.getSourceBeforeInStockQty()).isEqualTo(65);
//         assertThat(log3.getSourceAfterInStockQty()).isEqualTo(55);
//         assertThat(log3.getSourceBeforeInStockQty()).isEqualTo(log2.getSourceAfterInStockQty());
//
//         // 验证整个链条的连续性
//         for (int i = 1; i < logChain.size(); i++) {
//             BinLocationLog prevLog = logChain.get(i - 1);
//             BinLocationLog currentLog = logChain.get(i);
//
//             assertThat(currentLog.getSourceBeforeInStockQty())
//                 .as("第%d次操作的beforeQty应该等于第%d次操作的afterQty", i + 1, i)
//                 .isEqualTo(prevLog.getSourceAfterInStockQty());
//         }
//     }
//
//     @Test
//     @DisplayName("测试跨库位操作的日志记录")
//     void testCrossLocationOperationLog() {
//         // 准备测试数据 - 不同库位
//         BinLocationDetail sourceLocation = new BinLocationDetail();
//         sourceLocation.setId(1L);
//         sourceLocation.setBinLocationId(100L);
//         sourceLocation.setProductId(200L);
//         sourceLocation.setProductVersionId(300L);
//         sourceLocation.setInStockQty(50);
//
//         BinLocationDetail destLocation = new BinLocationDetail();
//         destLocation.setId(2L);
//         destLocation.setBinLocationId(101L);
//         destLocation.setProductId(200L);
//         destLocation.setProductVersionId(300L);
//         destLocation.setInStockQty(30);
//
//         RefTableBO refTable = new RefTableBO();
//         refTable.setRefTableId(1L);
//         refTable.setRefTableName("TestTable");
//         refTable.setRefTableRefNum("TEST001");
//
//         // 创建变更对象
//         BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
//         changeBO.setSource(sourceLocation);
//         changeBO.setDest(destLocation); // 不同库位
//         changeBO.setChangeQty(15);
//         changeBO.setChangeType("TRANSFER");
//         changeBO.setRefTable(refTable);
//
//         // 执行移动操作
//         changeBO.move();
//
//         // 执行测试
//         BinLocationLog log = changeBO.toBinLocationLog();
//
//         // 验证结果
//         assertThat(log).isNotNull();
//
//         // 验证source信息
//         assertThat(log.getSourceBinLocationId()).isEqualTo(100L);
//         assertThat(log.getSourceBinLocationDetailId()).isEqualTo(1L);
//         assertThat(log.getSourceBeforeInStockQty()).isEqualTo(50);
//         assertThat(log.getSourceAfterInStockQty()).isEqualTo(35);
//         assertThat(log.getSourceChangeInStockQty()).isEqualTo(-15);
//
//         // 验证dest信息（跨库位操作应该记录dest信息）
//         assertThat(log.getDestBinLocationId()).isEqualTo(101L);
//         assertThat(log.getDestBinLocationDetailId()).isEqualTo(2L);
//         assertThat(log.getDestBeforeInStockQty()).isEqualTo(30);
//         assertThat(log.getDestAfterInStockQty()).isEqualTo(45);
//         assertThat(log.getDestChangeInStockQty()).isEqualTo(15);
//     }
//
//     @Test
//     @DisplayName("测试修复前后的差异对比")
//     void testBeforeAfterFixComparison() {
//         // 这个测试展示修复前后的差异
//         BinLocationDetail binLocation = new BinLocationDetail();
//         binLocation.setId(1L);
//         binLocation.setBinLocationId(100L);
//         binLocation.setProductId(200L);
//         binLocation.setProductVersionId(300L);
//         binLocation.setInStockQty(100);
//
//         RefTableBO refTable = new RefTableBO();
//         refTable.setRefTableId(1L);
//         refTable.setRefTableName("TestTable");
//         refTable.setRefTableRefNum("TEST001");
//
//         // 模拟修复前的逻辑（计算得出的beforeQty）
//         BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
//         changeBO.setSource(binLocation);
//         changeBO.setDest(binLocation);
//         changeBO.setChangeQty(20);
//         changeBO.setChangeType("SHIP");
//         changeBO.setRefTable(refTable);
//
//         // 不调用move()，直接修改库存（模拟修复前的情况）
//         int originalQty = binLocation.getInStockQty();
//         binLocation.setInStockQty(binLocation.getInStockQty() - 20);
//
//         // 修复前：通过计算得出beforeQty
//         int calculatedBeforeQty = binLocation.getInStockQty() + 20; // 80 + 20 = 100
//
//         // 修复后：使用真实记录的beforeQty
//         changeBO.setSourceBeforeInStockQty(originalQty); // 直接记录原始值100
//
//         BinLocationLog log = changeBO.toBinLocationLog();
//
//         // 验证修复后的结果
//         assertThat(log.getSourceBeforeInStockQty()).isEqualTo(100); // 真实记录的值
//         assertThat(log.getSourceAfterInStockQty()).isEqualTo(80);   // 修改后的值
//         assertThat(calculatedBeforeQty).isEqualTo(100); // 计算值碰巧相等，但在并发情况下可能不同
//
//         // 在并发或批量操作中，计算值可能不准确，但真实记录值始终准确
//     }
// }
