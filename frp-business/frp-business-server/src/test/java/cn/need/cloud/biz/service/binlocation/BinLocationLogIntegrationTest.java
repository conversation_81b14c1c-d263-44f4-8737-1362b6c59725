package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.entity.transfer.TransferOwnerShipRequest;
import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.core.session.model.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * BinLocationLog 集成测试类
 * 测试各种操作（transferownership、inventoryaudit、pick、putaway）的 binlocationlog 记录逻辑
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@ExtendWith(MockitoExtension.class)
class BinLocationLogIntegrationTest {

    @Mock
    private BinLocationLogService binLocationLogService;

    private UserInfo mockUser;

    @BeforeEach
    void setUp() {
        mockUser = new UserInfo();
        mockUser.setId(1L);
        mockUser.setName("testUser");
    }

    /**
     * 测试 TransferOwnership 操作的 BinLocationLog 记录
     * 验证所有权转移操作不会产生重复的日志记录
     */
    @Test
    void testTransferOwnership_BinLocationLogRecording() {
        try (MockedStatic<Users> usersMock = mockStatic(Users.class)) {
            usersMock.when(Users::getUser).thenReturn(mockUser);

            // 创建测试数据 - 同一库位的所有权转移
            BinLocationDetail binLocationDetail = createTestBinLocationDetail(1L, 1L, 100, 1001L);
            
            BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
            changeBO.setSource(binLocationDetail);
            changeBO.setDest(binLocationDetail); // 同一库位
            changeBO.setChangeQty(50);
            changeBO.setChangeType(BinLocationLogEnum.TRANSFER_OWNERSHIP.getStatus());
            changeBO.setRefTable(createTransferOwnershipRefTable());

            // 执行测试
            BinLocationLog log = changeBO.toBinLocationLog();

            // 验证结果 - 同一库位的所有权转移应该只记录一条日志
            assertNotNull(log);
            assertEquals(BinLocationLogEnum.TRANSFER_OWNERSHIP.getStatus(), log.getChangeType());
            assertEquals(binLocationDetail.getBinLocationId(), log.getSourceBinLocationId());
            assertEquals(-50, log.getSourceChangeInStockQty());
            
            // 修复后：dest信息应该为null，避免重复记录
            assertNull(log.getDestBinLocationId());
            assertNull(log.getDestChangeInStockQty());
            
            // 验证关联表信息正确
            assertEquals("TransferOwnerShipRequest", log.getRefTableName());
            assertEquals("TRANSFER001", log.getRefTableRefNum());
        }
    }

    /**
     * 测试 InventoryAudit 操作的 BinLocationLog 记录
     * 验证库存盘点操作不会产生重复的日志记录
     */
    @Test
    void testInventoryAudit_BinLocationLogRecording() {
        try (MockedStatic<Users> usersMock = mockStatic(Users.class)) {
            usersMock.when(Users::getUser).thenReturn(mockUser);

            // 创建测试数据 - 库存盘点调整
            BinLocationDetail binLocationDetail = createTestBinLocationDetail(1L, 1L, 80, 1001L);
            
            BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
            changeBO.setSource(binLocationDetail);
            changeBO.setDest(binLocationDetail); // 同一库位
            changeBO.setChangeQty(20); // 盘点增加20个
            changeBO.setChangeType(BinLocationLogEnum.INVENTORY_AUDIT.getStatus());
            changeBO.setRefTable(createInventoryAuditRefTable());

            // 执行测试
            BinLocationLog log = changeBO.toBinLocationLog();

            // 验证结果 - 库存盘点应该只记录一条日志
            assertNotNull(log);
            assertEquals(BinLocationLogEnum.INVENTORY_AUDIT.getStatus(), log.getChangeType());
            assertEquals(binLocationDetail.getBinLocationId(), log.getSourceBinLocationId());
            assertEquals(-20, log.getSourceChangeInStockQty()); // 注意：这里是负数，因为是从原库存中扣减
            
            // 修复后：dest信息应该为null
            assertNull(log.getDestBinLocationId());
            assertNull(log.getDestChangeInStockQty());
            
            // 验证关联表信息正确
            assertEquals("InventoryAudit", log.getRefTableName());
            assertEquals("AUDIT001", log.getRefTableRefNum());
        }
    }

    /**
     * 测试 Pick 操作的 BinLocationLog 记录
     * 验证拣货操作不会产生重复的日志记录
     */
    @Test
    void testPick_BinLocationLogRecording() {
        try (MockedStatic<Users> usersMock = mockStatic(Users.class)) {
            usersMock.when(Users::getUser).thenReturn(mockUser);

            // 创建测试数据 - 拣货操作
            BinLocationDetail binLocationDetail = createTestBinLocationDetail(1L, 1L, 150, 1001L);
            
            BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
            changeBO.setSource(binLocationDetail);
            changeBO.setDest(binLocationDetail); // 同一库位
            changeBO.setChangeQty(30); // 拣货30个
            changeBO.setChangeType(BinLocationLogEnum.OTC_PICK.getStatus());
            changeBO.setRefTable(createPickRefTable());

            // 执行测试
            BinLocationLog log = changeBO.toBinLocationLog();

            // 验证结果 - 拣货操作应该只记录一条日志
            assertNotNull(log);
            assertEquals(BinLocationLogEnum.OTC_PICK.getStatus(), log.getChangeType());
            assertEquals(binLocationDetail.getBinLocationId(), log.getSourceBinLocationId());
            assertEquals(-30, log.getSourceChangeInStockQty());
            
            // 修复后：dest信息应该为null
            assertNull(log.getDestBinLocationId());
            assertNull(log.getDestChangeInStockQty());
            
            // 验证关联表信息正确
            assertEquals("OtcPickingSlipDetail", log.getRefTableName());
            assertEquals("PICK001", log.getRefTableRefNum());
        }
    }

    /**
     * 测试 PutAway 操作的 BinLocationLog 记录
     * 验证上架操作不会产生重复的日志记录
     */
    @Test
    void testPutAway_BinLocationLogRecording() {
        try (MockedStatic<Users> usersMock = mockStatic(Users.class)) {
            usersMock.when(Users::getUser).thenReturn(mockUser);

            // 创建测试数据 - 上架操作
            BinLocationDetail binLocationDetail = createTestBinLocationDetail(1L, 1L, 50, 1001L);
            
            BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
            changeBO.setSource(binLocationDetail);
            changeBO.setDest(binLocationDetail); // 同一库位
            changeBO.setChangeQty(-40); // 上架增加库存，所以changeQty为负数
            changeBO.setChangeType(BinLocationLogEnum.OTC_PREP_PUT_AWAY.getStatus());
            changeBO.setRefTable(createPutAwayRefTable());

            // 执行测试
            BinLocationLog log = changeBO.toBinLocationLog();

            // 验证结果 - 上架操作应该只记录一条日志
            assertNotNull(log);
            assertEquals(BinLocationLogEnum.OTC_PREP_PUT_AWAY.getStatus(), log.getChangeType());
            assertEquals(binLocationDetail.getBinLocationId(), log.getSourceBinLocationId());
            assertEquals(40, log.getSourceChangeInStockQty()); // 库存增加
            
            // 修复后：dest信息应该为null
            assertNull(log.getDestBinLocationId());
            assertNull(log.getDestChangeInStockQty());
            
            // 验证关联表信息正确
            assertEquals("OtcPrepPickingSlip", log.getRefTableName());
            assertEquals("PUTAWAY001", log.getRefTableRefNum());
        }
    }

    /**
     * 测试跨库位移动的 BinLocationLog 记录
     * 验证不同库位之间的移动操作记录完整的source和dest信息
     */
    @Test
    void testCrossBinLocationMove_BinLocationLogRecording() {
        try (MockedStatic<Users> usersMock = mockStatic(Users.class)) {
            usersMock.when(Users::getUser).thenReturn(mockUser);

            // 创建测试数据 - 跨库位移动
            BinLocationDetail sourceBinLocation = createTestBinLocationDetail(1L, 1L, 100, 1001L);
            BinLocationDetail destBinLocation = createTestBinLocationDetail(2L, 2L, 50, 1001L);
            
            BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
            changeBO.setSource(sourceBinLocation);
            changeBO.setDest(destBinLocation); // 不同库位
            changeBO.setChangeQty(25);
            changeBO.setChangeType(BinLocationLogEnum.TRANSFER_OWNERSHIP.getStatus());
            changeBO.setRefTable(createTransferOwnershipRefTable());

            // 执行测试
            BinLocationLog log = changeBO.toBinLocationLog();

            // 验证结果 - 跨库位移动应该记录完整的source和dest信息
            assertNotNull(log);
            assertEquals(BinLocationLogEnum.TRANSFER_OWNERSHIP.getStatus(), log.getChangeType());
            
            // Source信息
            assertEquals(sourceBinLocation.getBinLocationId(), log.getSourceBinLocationId());
            assertEquals(-25, log.getSourceChangeInStockQty());
            assertEquals(125, log.getSourceBeforeInStockQty());
            assertEquals(100, log.getSourceAfterInStockQty());
            
            // Dest信息
            assertEquals(destBinLocation.getBinLocationId(), log.getDestBinLocationId());
            assertEquals(25, log.getDestChangeInStockQty());
            assertEquals(25, log.getDestBeforeInStockQty());
            assertEquals(50, log.getDestAfterInStockQty());
        }
    }

    /**
     * 创建测试库位详情数据
     */
    private BinLocationDetail createTestBinLocationDetail(Long id, Long binLocationId, Integer inStockQty, Long productId) {
        BinLocationDetail detail = new BinLocationDetail();
        detail.setId(id);
        detail.setBinLocationId(binLocationId);
        detail.setInStockQty(inStockQty);
        detail.setProductId(productId);
        detail.setProductVersionId(1L);
        return detail;
    }

    /**
     * 创建 TransferOwnership 关联表信息
     */
    private RefTableBO createTransferOwnershipRefTable() {
        RefTableBO refTable = new RefTableBO();
        refTable.setRefTableId(1L);
        refTable.setRefTableName("TransferOwnerShipRequest");
        refTable.setRefTableRefNum("TRANSFER001");
        refTable.setRefTableShowName("TransferOwnerShipRequest");
        refTable.setRefTableShowRefNum("TRANSFER001");
        return refTable;
    }

    /**
     * 创建 InventoryAudit 关联表信息
     */
    private RefTableBO createInventoryAuditRefTable() {
        RefTableBO refTable = new RefTableBO();
        refTable.setRefTableId(1L);
        refTable.setRefTableName("InventoryAudit");
        refTable.setRefTableRefNum("AUDIT001");
        refTable.setRefTableShowName("InventoryAudit");
        refTable.setRefTableShowRefNum("AUDIT001");
        return refTable;
    }

    /**
     * 创建 Pick 关联表信息
     */
    private RefTableBO createPickRefTable() {
        RefTableBO refTable = new RefTableBO();
        refTable.setRefTableId(1L);
        refTable.setRefTableName("OtcPickingSlipDetail");
        refTable.setRefTableRefNum("PICK001");
        refTable.setRefTableShowName("OtcPickingSlip");
        refTable.setRefTableShowRefNum("PICK001");
        return refTable;
    }

    /**
     * 创建 PutAway 关联表信息
     */
    private RefTableBO createPutAwayRefTable() {
        RefTableBO refTable = new RefTableBO();
        refTable.setRefTableId(1L);
        refTable.setRefTableName("OtcPrepPickingSlip");
        refTable.setRefTableRefNum("PUTAWAY001");
        refTable.setRefTableShowName("OtcPrepPickingSlip");
        refTable.setRefTableShowRefNum("PUTAWAY001");
        return refTable;
    }
}
