package cn.need.cloud.biz.model.bo.binlocation;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.vo.base.auditlog.BaseProductLogVO;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.Builder;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

/**
 * 库位移动
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Data
public class BinLocationDetailChangeBO implements Serializable {

    /**
     * 原库位
     */
    protected BinLocationDetail source;

    /**
     * 原库位锁
     */
    protected BinLocationDetailLocked sourceLock;

    /**
     * 目标库位
     */
    protected BinLocationDetail dest;

    /**
     * 目标库位锁
     */
    protected BinLocationDetailLocked destLock;

    /**
     * 移动数量
     */
    protected Integer changeQty;

    /**
     * 关联表
     */
    protected RefTableBO refTable;

    /**
     * 变更类型
     */
    protected String changeType;

    /**
     * 备注
     */
    protected String note;

    /**
     * 源库位移动前的库存数量（用于确保日志链条连续性）
     */
    protected Integer sourceBeforeInStockQty;

    /**
     * 目标库位移动前的库存数量（用于确保日志链条连续性）
     */
    protected Integer destBeforeInStockQty;

    /**
     * 源库位移动数量并返回库位数量
     * 修复：在移动前记录原始库存数量，确保日志链条的连续性
     */
    public void move() {
        // 记录移动前的库存数量，用于日志记录
        this.sourceBeforeInStockQty = source.getInStockQty();
        this.destBeforeInStockQty = dest.getInStockQty();

        source.setInStockQty(source.getInStockQty() - changeQty);
        if (!isSame()) {
            // 库位不一致需要移动
            dest.setInStockQty(dest.getInStockQty() + changeQty);
        }
        Validate.isTrue(source.getInStockQty() >= 0,
                "{} insufficient inventory, needed {} qty",
                toLog(), source.getInStockQty()
        );
    }

    /**
     * 源库位移动数量并返回库位数量
     *
     * @return 库存
     */
    public int moveLock() {
        sourceLock.setFinishQty(sourceLock.getFinishQty() + changeQty);
        if (!isSame()) {
            // 库位不一致需要移动
            destLock.setQty(destLock.getQty() + changeQty);
        }
        return sourceLock.getFinishQty();
    }

    /**
     * 源库位与目标库位一致
     *
     * @return 是否一致
     */
    public boolean isSame() {
        return source == dest || (source != null && dest != null && Objects.equals(source.getId(), dest.getId()));
    }

    public String toLog() {
        return StringUtil.format("Source BinLocation：{}, Dest BinLocation：{}, Product: {}, ChangeQty: {} ",
                Optional.ofNullable(source.getBinLocationId())
                        .map(BinLocationCacheUtil::getById)
                        .map(BinLocationCache::getLocationName)
                        .orElse(StringPool.EMPTY),
                Optional.ofNullable(dest.getBinLocationId())
                        .map(BinLocationCacheUtil::getById)
                        .map(BinLocationCache::getLocationName)
                        .orElse(StringPool.EMPTY),
                Optional.ofNullable(source.getProductId())
                        .map(ProductCacheUtil::getById)
                        .map(cache -> BeanUtil.copyNew(cache, BaseProductLogVO.class))
                        .map(BaseProductLogVO::toLog)
                        .orElse(StringPool.EMPTY),
                getChangeQty()
        );
    }

    public BinLocationLog toBinLocationLog() {
        return Builder.of(BinLocationLog::new)
                // 创建用户
                .with(BinLocationLog::setCreateBy, Objects.requireNonNull(Users.getUser()).getId())
                // 更新用户
                .with(BinLocationLog::setUpdateBy, Objects.requireNonNull(Users.getUser()).getId())
                .with(BinLocationLog::setRefTableId, refTable.getRefTableId())
                .with(BinLocationLog::setRefTableName, refTable.getRefTableName())
                .with(BinLocationLog::setRefTableRefNum, refTable.getRefTableRefNum())
                .with(BinLocationLog::setRefTableShowName, refTable.getRefTableShowName())
                .with(BinLocationLog::setRefTableShowRefNum, refTable.getRefTableShowRefNum())
                // 产品
                .with(BinLocationLog::setProductId, source.getProductId())
                .with(BinLocationLog::setProductVersionId, source.getProductVersionId())
                .with(BinLocationLog::setChangeType, changeType)
                // Source
                .with(BinLocationLog::setSourceBinLocationId, source.getBinLocationId())
                .with(BinLocationLog::setSourceBinLocationDetailId, source.getId())
                .with(BinLocationLog::setSourceAfterInStockQty, source.getInStockQty())
                // 修复：使用真实记录的移动前数量，确保日志链条连续性
                .with(BinLocationLog::setSourceBeforeInStockQty, sourceBeforeInStockQty != null ? sourceBeforeInStockQty : source.getInStockQty() + changeQty)
                .with(BinLocationLog::setSourceChangeInStockQty, -changeQty)
                // Dest - 修复：正确记录dest信息，同一库位操作时记录相同的数据
                .with(BinLocationLog::setDestBinLocationId, dest.getBinLocationId())
                .with(BinLocationLog::setDestBinLocationDetailId, dest.getId())
                // 修复：对于同一库位操作，dest和source记录相同的change_qty
                .with(BinLocationLog::setDestChangeInStockQty, isSame() ? -changeQty : changeQty)
                .with(BinLocationLog::setDestAfterInStockQty, dest.getInStockQty())
                // 修复：对于同一库位操作，dest_before_qty等于source_before_qty
                .with(BinLocationLog::setDestBeforeInStockQty, isSame() ?
                    (sourceBeforeInStockQty != null ? sourceBeforeInStockQty : dest.getInStockQty() + changeQty) :
                    (destBeforeInStockQty != null ? destBeforeInStockQty : dest.getInStockQty() - changeQty))
                // 备注
                .with(BinLocationLog::setNote, note)
                .build();
    }

    public String toLockLog() {
        return isSame()
                ? StringUtil.format("RefNum: {} BinLocationDetailLocked: {} ChangeQty: {} ",
                sourceLock.getRefTableShowRefNum(), sourceLock.getId(), getChangeQty())
                : StringUtil.format("RefNum: {} SourceBinLocationDetailLocked：{}, DestBinLocationDetailLocked：{}, ChangeQty: {}",
                sourceLock.getRefTableShowRefNum(), sourceLock.getId(), destLock.getId(), getChangeQty());
    }
}
