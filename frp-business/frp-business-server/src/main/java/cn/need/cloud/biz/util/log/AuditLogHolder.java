package cn.need.cloud.biz.util.log;

import cn.hutool.core.util.ObjectUtil;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.framework.common.core.exception.unchecked.ImpossibleException;
import cn.need.framework.common.core.lang.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/***
 * 日志上下文
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public class AuditLogHolder {

    /**
     * AuditShow日志上下文
     */
    private static final ThreadLocal<List<AuditShowLog>> AUDIT_LOG_CONTEXT = new ThreadLocal<>();

    /**
     * 库位日志上下文
     */
    private static final ThreadLocal<List<BinLocationLog>> BIN_LOCATION_LOG_CONTEXT = new ThreadLocal<>();

    /**
     * 初始化
     */
    public static void init() {
        // 初始化
        AUDIT_LOG_CONTEXT.set(new CopyOnWriteArrayList<>());
        BIN_LOCATION_LOG_CONTEXT.set(new CopyOnWriteArrayList<>());
    }

    /**
     * 添加日志
     *
     * @param log 日志
     */
    public static void record(AuditShowLog log) {
        AUDIT_LOG_CONTEXT.get().add(log);
    }

    /**
     * 添加日志
     *
     * @param logList 日志
     */
    public static void record(List<AuditShowLog> logList) {
        AUDIT_LOG_CONTEXT.get().addAll(logList);
    }

    /**
     * 获取日志
     *
     * @return 日志
     */
    public static List<AuditShowLog> getAndClearShowLog() {
        List<AuditShowLog> showLogs = new ArrayList<>(AUDIT_LOG_CONTEXT.get());
        AUDIT_LOG_CONTEXT.get().clear();
        return showLogs;
    }

    /**
     * 添加日志
     *
     * @param log 日志
     */
    public static void binLocationRecord(BinLocationLog log) {
        if (ObjectUtil.isEmpty(log)) {
            return;
        }
        checkBinLocationLog(log);

        BIN_LOCATION_LOG_CONTEXT.get().add(log);
    }

    private static void checkBinLocationLog(BinLocationLog log) {

        if (ObjectUtil.isEmpty(log.getSourceBeforeInStockQty()) || ObjectUtil.isEmpty(log.getSourceChangeInStockQty()) || ObjectUtil.isEmpty(log.getSourceAfterInStockQty())) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "source_before_in_stock_qty or source_change_in_stock_qty or source_after_in_stock_qty is null", log));
        }

        if (ObjectUtil.isEmpty(log.getDestBeforeInStockQty()) || ObjectUtil.isEmpty(log.getDestChangeInStockQty()) || ObjectUtil.isEmpty(log.getDestAfterInStockQty())) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "dest_before_in_stock_qty or dest_change_in_stock_qty or dest_after_in_stock_qty is null", log));
        }

        if (log.getSourceBeforeInStockQty() < 0) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "source_before_in_stock_qty < 0", log));
        }

        if (log.getSourceAfterInStockQty() < 0) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "source_after_in_stock_qty < 0", log));
        }

        if (log.getDestBeforeInStockQty() < 0) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "dest_before_in_stock_qty < 0", log));
        }

        if (log.getDestAfterInStockQty() < 0) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "dest_after_in_stock_qty < 0", log));
        }

        if (log.getDestBeforeInStockQty() + log.getDestChangeInStockQty() != log.getDestAfterInStockQty()) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: dest_before_qty({}) + dest_change_qty({}) != dest_after_qty({}), log: {}",
                    log.getDestBeforeInStockQty(), log.getDestChangeInStockQty(), log.getDestAfterInStockQty(), log));
        }
        if (log.getSourceBeforeInStockQty() + log.getSourceChangeInStockQty() != log.getSourceAfterInStockQty()) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: source_before_qty({}) + source_change_qty({}) != source_after_qty({}), log: {}",
                    log.getSourceBeforeInStockQty(), log.getSourceChangeInStockQty(), log.getSourceAfterInStockQty(), log));
        }

        if (log.getDestBinLocationDetailId() == null || log.getSourceBinLocationDetailId() == null) {
            throw new ImpossibleException(StringUtil.format(
                    "BinLocationLog数据约束检查失败: {}", "dest_bin_location_detail_id or source_bin_location_detail_id is null", log));
        }
        // 同一库位操作的约束检查
        if (log.getDestBinLocationDetailId().equals(log.getSourceBinLocationDetailId())) {
            // 修复：对于同一库位操作，dest和source应该记录相同的数据
            if (!log.getDestChangeInStockQty().equals(log.getSourceChangeInStockQty())) {
                throw new ImpossibleException(StringUtil.format(
                        "BinLocationLog数据约束检查失败: 同一库位操作dest_change_qty({}) != source_change_qty({}), log: {}",
                        log.getDestChangeInStockQty(), log.getSourceChangeInStockQty(), log));
            }
            if (!log.getDestBeforeInStockQty().equals(log.getSourceBeforeInStockQty())) {
                throw new ImpossibleException(StringUtil.format(
                        "BinLocationLog数据约束检查失败: 同一库位操作dest_before_qty({}) != source_before_qty({}), log: {}",
                        log.getDestBeforeInStockQty(), log.getSourceBeforeInStockQty(), log));
            }
            if (!log.getDestAfterInStockQty().equals(log.getSourceAfterInStockQty())) {
                throw new ImpossibleException(StringUtil.format(
                        "BinLocationLog数据约束检查失败: 同一库位操作dest_after_qty({}) != source_after_qty({}), log: {}",
                        log.getDestAfterInStockQty(), log.getSourceAfterInStockQty(), log));
            }
        }
    }

    /**
     * 添加日志
     *
     * @param logList 日志
     */
    public static void binLocationRecord(List<BinLocationLog> logList) {
        if (ObjectUtil.isEmpty(logList)) {
            return;
        }
        logList.forEach(AuditLogHolder::binLocationRecord);
    }

    /**
     * 获取日志
     *
     * @return 日志
     */
    public static List<BinLocationLog> getAndClearBinLocationLog() {
        List<BinLocationLog> showLogs = new ArrayList<>(BIN_LOCATION_LOG_CONTEXT.get());
        BIN_LOCATION_LOG_CONTEXT.get().clear();
        return showLogs;
    }

}
