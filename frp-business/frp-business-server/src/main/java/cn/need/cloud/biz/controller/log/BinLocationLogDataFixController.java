package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.service.log.BinLocationLogDataFixService;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataFixResult;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataAnalysisResult;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataValidationResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * BinLocationLog 数据修复控制器
 * 
 * 提供数据修复相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/binlocationlog/datafix")
@Api(tags = "BinLocationLog数据修复")
@RequiredArgsConstructor
public class BinLocationLogDataFixController {

    private final BinLocationLogDataFixService dataFixService;

    @GetMapping("/analyze")
    @ApiOperation("分析数据问题")
    public DataAnalysisResult analyzeDataProblems(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始分析 BinLocationLog 数据问题，起始时间: {}", startTime);
        return dataFixService.analyzeDataProblems(startTime);
    }

    @PostMapping("/fix/inventory-audit")
    @ApiOperation("修复 inventoryaudit 数据")
    public Integer fixInventoryAuditData(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始修复 inventoryaudit 数据，起始时间: {}", startTime);
        return dataFixService.fixInventoryAuditData(startTime);
    }

    @PostMapping("/fix/chain-continuity")
    @ApiOperation("重建链条连续性")
    public Integer rebuildChainContinuity(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始重建链条连续性，起始时间: {}", startTime);
        return dataFixService.rebuildChainContinuity(startTime);
    }

    @PostMapping("/fix/clean-duplicates")
    @ApiOperation("清理重复记录")
    public Integer cleanDuplicateDestRecords(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始清理重复记录，起始时间: {}", startTime);
        return dataFixService.cleanDuplicateDestRecords(startTime);
    }

    @GetMapping("/validate")
    @ApiOperation("验证修复结果")
    public DataValidationResult validateFixResult(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始验证修复结果，起始时间: {}", startTime);
        return dataFixService.validateFixResult(startTime);
    }

    @PostMapping("/fix/full")
    @ApiOperation("执行完整修复流程")
    public DataFixResult executeFullDataFix(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始执行完整数据修复流程，起始时间: {}", startTime);
        return dataFixService.executeFullDataFix(startTime);
    }

    @PostMapping("/fix/step-by-step")
    @ApiOperation("分步骤执行修复")
    public StepByStepFixResult executeStepByStepFix(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始分步骤执行数据修复，起始时间: {}", startTime);
        
        StepByStepFixResult result = new StepByStepFixResult();
        
        try {
            // 步骤1：分析问题
            log.info("步骤1：分析数据问题");
            result.setStep1Analysis(dataFixService.analyzeDataProblems(startTime));
            
            // 步骤2：修复 inventoryaudit
            log.info("步骤2：修复 inventoryaudit 数据");
            result.setStep2InventoryAuditFix(dataFixService.fixInventoryAuditData(startTime));
            
            // 步骤3：重建链条
            log.info("步骤3：重建链条连续性");
            result.setStep3ChainRebuild(dataFixService.rebuildChainContinuity(startTime));
            
            // 步骤4：清理重复
            log.info("步骤4：清理重复记录");
            result.setStep4DuplicateClean(dataFixService.cleanDuplicateDestRecords(startTime));
            
            // 步骤5：验证结果
            log.info("步骤5：验证修复结果");
            result.setStep5Validation(dataFixService.validateFixResult(startTime));
            
            result.setSuccess(true);
            log.info("分步骤数据修复完成: {}", result);
            
        } catch (Exception e) {
            log.error("分步骤数据修复失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }

    /**
     * 分步骤修复结果
     */
    public static class StepByStepFixResult {
        private boolean success;
        private String errorMessage;
        private DataAnalysisResult step1Analysis;
        private Integer step2InventoryAuditFix;
        private Integer step3ChainRebuild;
        private Integer step4DuplicateClean;
        private DataValidationResult step5Validation;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public DataAnalysisResult getStep1Analysis() { return step1Analysis; }
        public void setStep1Analysis(DataAnalysisResult step1Analysis) { this.step1Analysis = step1Analysis; }
        public Integer getStep2InventoryAuditFix() { return step2InventoryAuditFix; }
        public void setStep2InventoryAuditFix(Integer step2InventoryAuditFix) { this.step2InventoryAuditFix = step2InventoryAuditFix; }
        public Integer getStep3ChainRebuild() { return step3ChainRebuild; }
        public void setStep3ChainRebuild(Integer step3ChainRebuild) { this.step3ChainRebuild = step3ChainRebuild; }
        public Integer getStep4DuplicateClean() { return step4DuplicateClean; }
        public void setStep4DuplicateClean(Integer step4DuplicateClean) { this.step4DuplicateClean = step4DuplicateClean; }
        public DataValidationResult getStep5Validation() { return step5Validation; }
        public void setStep5Validation(DataValidationResult step5Validation) { this.step5Validation = step5Validation; }
        
        @Override
        public String toString() {
            return String.format("StepByStepFixResult{success=%s, analysis=%s, auditFix=%d, chainRebuild=%d, duplicateClean=%d, validation=%s}", 
                success, 
                step1Analysis != null ? step1Analysis.toString() : "null",
                step2InventoryAuditFix != null ? step2InventoryAuditFix : 0,
                step3ChainRebuild != null ? step3ChainRebuild : 0,
                step4DuplicateClean != null ? step4DuplicateClean : 0,
                step5Validation != null ? step5Validation.isOverallValid() : "unknown");
        }
    }
}
