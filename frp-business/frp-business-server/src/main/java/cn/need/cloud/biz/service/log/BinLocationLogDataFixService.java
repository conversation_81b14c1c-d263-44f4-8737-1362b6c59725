package cn.need.cloud.biz.service.log;

import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * BinLocationLog 数据修复服务
 * <p>
 * 用于修复数据库中已有的错误 binlocationlog 数据
 * 重建正确的链条连续性
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BinLocationLogDataFixService {

    private final BinLocationLogService binLocationLogService;

    /**
     * 执行完整的数据修复流程
     *
     * @param startTime 修复数据的起始时间
     * @return 修复结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public DataFixResult executeFullDataFix(LocalDateTime startTime) {
        log.info("开始执行 BinLocationLog 数据修复，起始时间: {}", startTime);

        DataFixResult result = new DataFixResult();

        try {
            // 1. 分析当前数据问题
            result.setAnalysisResult(analyzeDataProblems(startTime));
            log.info("数据问题分析完成: {}", result.getAnalysisResult());

            // 2. 修复 inventoryaudit 类型的数据
            result.setInventoryAuditFixCount(fixInventoryAuditData(startTime));
            log.info("inventoryaudit 数据修复完成，修复记录数: {}", result.getInventoryAuditFixCount());

            // 3. 重建链条连续性
            result.setChainFixCount(rebuildChainContinuity(startTime));
            log.info("链条连续性重建完成，修复记录数: {}", result.getChainFixCount());

            // 4. 验证数据完整性（不需要清理dest记录，同一库位操作应该记录dest信息）
            // result.setDuplicateCleanCount(cleanDuplicateDestRecords(startTime));
            // log.info("重复记录清理完成，清理记录数: {}", result.getDuplicateCleanCount());

            // 5. 验证修复结果
            result.setValidationResult(validateFixResult(startTime));
            log.info("修复结果验证完成: {}", result.getValidationResult());

            result.setSuccess(true);
            log.info("BinLocationLog 数据修复完成: {}", result);

        } catch (Exception e) {
            log.error("BinLocationLog 数据修复失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            throw e;
        }

        return result;
    }

    /**
     * 分析当前数据问题
     */
    public DataAnalysisResult analyzeDataProblems(LocalDateTime startTime) {
        log.info("开始分析数据问题，起始时间: {}", startTime);

        // 查询所有需要分析的记录
        QueryWrapper<BinLocationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("create_time", startTime)
                .isNotNull("source_bin_location_id")
                .orderByAsc("source_bin_location_id", "create_time");

        List<BinLocationLog> logs = binLocationLogService.list(queryWrapper);

        // 按库位分组
        Map<Long, List<BinLocationLog>> logsByLocation = logs.stream()
                .collect(Collectors.groupingBy(BinLocationLog::getSourceBinLocationId));

        int totalRecords = logs.size();
        int brokenChainCount = 0;
        int mathErrorCount = 0;
        int duplicateDestCount = 0;

        for (Map.Entry<Long, List<BinLocationLog>> entry : logsByLocation.entrySet()) {
            List<BinLocationLog> locationLogs = entry.getValue();

            for (int i = 0; i < locationLogs.size(); i++) {
                BinLocationLog currentLog = locationLogs.get(i);

                // 检查数学关系
                if (currentLog.getSourceBeforeInStockQty() != null &&
                        currentLog.getSourceAfterInStockQty() != null &&
                        currentLog.getSourceChangeInStockQty() != null) {

                    int calculatedAfter = currentLog.getSourceBeforeInStockQty() + currentLog.getSourceChangeInStockQty();
                    if (calculatedAfter != currentLog.getSourceAfterInStockQty()) {
                        mathErrorCount++;
                    }
                }

                // 检查链条连续性
                if (i > 0) {
                    BinLocationLog prevLog = locationLogs.get(i - 1);
                    if (prevLog.getSourceAfterInStockQty() != null &&
                            currentLog.getSourceBeforeInStockQty() != null &&
                            !prevLog.getSourceAfterInStockQty().equals(currentLog.getSourceBeforeInStockQty())) {
                        brokenChainCount++;
                    }
                }

                // 注意：同一库位操作时，source 和 dest 记录相同信息是正确的业务逻辑
                // 不再检查所谓的"重复 dest 记录"
            }
        }

        DataAnalysisResult result = new DataAnalysisResult();
        result.setTotalRecords(totalRecords);
        result.setBrokenChainCount(brokenChainCount);
        result.setMathErrorCount(mathErrorCount);
        result.setDuplicateDestCount(duplicateDestCount);
        result.setBrokenChainPercentage(totalRecords > 0 ? (double) brokenChainCount / totalRecords * 100 : 0);

        log.info("数据问题分析结果: {}", result);
        return result;
    }

    /**
     * 修复 inventoryaudit 类型的数据
     * inventoryaudit 的 sourceafterqty, destafterqty, changeqty 是正确的
     * 需要修复 beforeqty = afterqty - changeqty
     */
    public int fixInventoryAuditData(LocalDateTime startTime) {
        log.info("开始修复 inventoryaudit 数据");

        QueryWrapper<BinLocationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("change_type", "INVENTORY_AUDIT")
                .ge("create_time", startTime);

        List<BinLocationLog> auditLogs = binLocationLogService.list(queryWrapper);
        int fixCount = 0;

        for (BinLocationLog log : auditLogs) {
            boolean needUpdate = false;

            // 修复 source beforeqty
            if (log.getSourceAfterInStockQty() != null && log.getSourceChangeInStockQty() != null) {
                int correctSourceBeforeQty = log.getSourceAfterInStockQty() - log.getSourceChangeInStockQty();
                if (!Objects.equals(correctSourceBeforeQty, log.getSourceBeforeInStockQty())) {
                    log.setSourceBeforeInStockQty(correctSourceBeforeQty);
                    needUpdate = true;
                }
            }

            // 修复 dest beforeqty
            if (log.getDestAfterInStockQty() != null && log.getDestChangeInStockQty() != null) {
                int correctDestBeforeQty = log.getDestAfterInStockQty() - log.getDestChangeInStockQty();
                if (!Objects.equals(correctDestBeforeQty, log.getDestBeforeInStockQty())) {
                    log.setDestBeforeInStockQty(correctDestBeforeQty);
                    needUpdate = true;
                }
            }

            if (needUpdate) {
                log.setUpdateTime(LocalDateTime.now());
                log.setUpdateBy(1L); // 系统用户ID
                binLocationLogService.update(log);
                fixCount++;
            }
        }

        log.info("inventoryaudit 数据修复完成，修复记录数: {}", fixCount);
        return fixCount;
    }

    /**
     * 重建链条连续性
     * 确保每条记录的 beforeqty 等于前一条记录的 afterqty
     */
    public int rebuildChainContinuity(LocalDateTime startTime) {
        log.info("开始重建链条连续性");

        QueryWrapper<BinLocationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("create_time", startTime)
                .isNotNull("source_bin_location_id")
                .in("change_type", "SHIP", "TRANSFER", "PICK", "PUT_AWAY")
                .orderByAsc("source_bin_location_id", "create_time");

        List<BinLocationLog> logs = binLocationLogService.list(queryWrapper);

        // 按库位分组
        Map<Long, List<BinLocationLog>> logsByLocation = logs.stream()
                .collect(Collectors.groupingBy(BinLocationLog::getSourceBinLocationId));

        int fixCount = 0;

        for (Map.Entry<Long, List<BinLocationLog>> entry : logsByLocation.entrySet()) {
            List<BinLocationLog> locationLogs = entry.getValue();

            for (int i = 1; i < locationLogs.size(); i++) { // 从第二条记录开始
                BinLocationLog currentLog = locationLogs.get(i);
                BinLocationLog prevLog = locationLogs.get(i - 1);

                if (prevLog.getSourceAfterInStockQty() != null) {
                    // 当前记录的 beforeqty 应该等于前一条记录的 afterqty
                    if (!prevLog.getSourceAfterInStockQty().equals(currentLog.getSourceBeforeInStockQty())) {
                        currentLog.setSourceBeforeInStockQty(prevLog.getSourceAfterInStockQty());
                        currentLog.setUpdateTime(LocalDateTime.now());
                        currentLog.setUpdateBy(1L);
                        binLocationLogService.update(currentLog);
                        fixCount++;
                    }
                }
            }
        }

        log.info("链条连续性重建完成，修复记录数: {}", fixCount);
        return fixCount;
    }


    /**
     * 验证修复结果
     */
    public DataValidationResult validateFixResult(LocalDateTime startTime) {
        log.info("开始验证修复结果");

        DataAnalysisResult afterFixAnalysis = analyzeDataProblems(startTime);

        DataValidationResult result = new DataValidationResult();
        result.setTotalRecords(afterFixAnalysis.getTotalRecords());
        result.setBrokenChainCount(afterFixAnalysis.getBrokenChainCount());
        result.setMathErrorCount(afterFixAnalysis.getMathErrorCount());
        result.setDuplicateDestCount(afterFixAnalysis.getDuplicateDestCount());
        result.setBrokenChainPercentage(afterFixAnalysis.getBrokenChainPercentage());

        // 验证成功标准
        result.setChainContinuityValid(result.getBrokenChainPercentage() < 1.0);
        result.setMathRelationValid(result.getMathErrorCount() == 0);
        result.setDuplicateCleanValid(result.getDuplicateDestCount() == 0);
        result.setOverallValid(result.isChainContinuityValid() &&
                result.isMathRelationValid() &&
                result.isDuplicateCleanValid());

        log.info("修复结果验证完成: {}", result);
        return result;
    }

    // 内部类定义
    public static class DataFixResult {
        private boolean success;
        private String errorMessage;
        private DataAnalysisResult analysisResult;
        private int inventoryAuditFixCount;
        private int chainFixCount;
        private int duplicateCleanCount;
        private DataValidationResult validationResult;

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public DataAnalysisResult getAnalysisResult() {
            return analysisResult;
        }

        public void setAnalysisResult(DataAnalysisResult analysisResult) {
            this.analysisResult = analysisResult;
        }

        public int getInventoryAuditFixCount() {
            return inventoryAuditFixCount;
        }

        public void setInventoryAuditFixCount(int inventoryAuditFixCount) {
            this.inventoryAuditFixCount = inventoryAuditFixCount;
        }

        public int getChainFixCount() {
            return chainFixCount;
        }

        public void setChainFixCount(int chainFixCount) {
            this.chainFixCount = chainFixCount;
        }

        public int getDuplicateCleanCount() {
            return duplicateCleanCount;
        }

        public void setDuplicateCleanCount(int duplicateCleanCount) {
            this.duplicateCleanCount = duplicateCleanCount;
        }

        public DataValidationResult getValidationResult() {
            return validationResult;
        }

        public void setValidationResult(DataValidationResult validationResult) {
            this.validationResult = validationResult;
        }

        @Override
        public String toString() {
            return String.format("DataFixResult{success=%s, inventoryAuditFix=%d, chainFix=%d, duplicateClean=%d, valid=%s}",
                    success, inventoryAuditFixCount, chainFixCount, duplicateCleanCount,
                    validationResult != null ? validationResult.isOverallValid() : "unknown");
        }
    }

    public static class DataAnalysisResult {
        private int totalRecords;
        private int brokenChainCount;
        private int mathErrorCount;
        private int duplicateDestCount;
        private double brokenChainPercentage;

        // getters and setters
        public int getTotalRecords() {
            return totalRecords;
        }

        public void setTotalRecords(int totalRecords) {
            this.totalRecords = totalRecords;
        }

        public int getBrokenChainCount() {
            return brokenChainCount;
        }

        public void setBrokenChainCount(int brokenChainCount) {
            this.brokenChainCount = brokenChainCount;
        }

        public int getMathErrorCount() {
            return mathErrorCount;
        }

        public void setMathErrorCount(int mathErrorCount) {
            this.mathErrorCount = mathErrorCount;
        }

        public int getDuplicateDestCount() {
            return duplicateDestCount;
        }

        public void setDuplicateDestCount(int duplicateDestCount) {
            this.duplicateDestCount = duplicateDestCount;
        }

        public double getBrokenChainPercentage() {
            return brokenChainPercentage;
        }

        public void setBrokenChainPercentage(double brokenChainPercentage) {
            this.brokenChainPercentage = brokenChainPercentage;
        }

        @Override
        public String toString() {
            return String.format("DataAnalysisResult{total=%d, brokenChain=%d(%.2f%%), mathError=%d, duplicate=%d}",
                    totalRecords, brokenChainCount, brokenChainPercentage, mathErrorCount, duplicateDestCount);
        }
    }

    public static class DataValidationResult extends DataAnalysisResult {
        private boolean chainContinuityValid;
        private boolean mathRelationValid;
        private boolean duplicateCleanValid;
        private boolean overallValid;

        // getters and setters
        public boolean isChainContinuityValid() {
            return chainContinuityValid;
        }

        public void setChainContinuityValid(boolean chainContinuityValid) {
            this.chainContinuityValid = chainContinuityValid;
        }

        public boolean isMathRelationValid() {
            return mathRelationValid;
        }

        public void setMathRelationValid(boolean mathRelationValid) {
            this.mathRelationValid = mathRelationValid;
        }

        public boolean isDuplicateCleanValid() {
            return duplicateCleanValid;
        }

        public void setDuplicateCleanValid(boolean duplicateCleanValid) {
            this.duplicateCleanValid = duplicateCleanValid;
        }

        public boolean isOverallValid() {
            return overallValid;
        }

        public void setOverallValid(boolean overallValid) {
            this.overallValid = overallValid;
        }

        @Override
        public String toString() {
            return String.format("DataValidationResult{total=%d, chain=%s, math=%s, duplicate=%s, overall=%s}",
                    getTotalRecords(), chainContinuityValid, mathRelationValid, duplicateCleanValid, overallValid);
        }
    }
}
