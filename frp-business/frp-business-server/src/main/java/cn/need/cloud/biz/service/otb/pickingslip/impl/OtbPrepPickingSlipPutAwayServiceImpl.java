package cn.need.cloud.biz.service.otb.pickingslip.impl;

import cn.hutool.core.lang.Pair;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepWorkOrderEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.inbound.BinLocationDetailContextBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipPutAwayQuery;
import cn.need.cloud.biz.model.vo.base.BasePutAwayVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderDetailPutAwayVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailPutAwayVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipPutAwayContextVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderPutAwayVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPutAwayLogVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipDetailService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipPutAwayService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipService;
import cn.need.cloud.biz.service.otb.workorder.*;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ImpossibleException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * otb预拣货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor
public class OtbPrepPickingSlipPutAwayServiceImpl implements OtbPrepPickingSlipPutAwayService {


    private final OtbPrepPickingSlipDetailService otbPrepPickingSlipDetailService;
    private final OtbPrepWorkorderService otbPrepWorkorderService;
    private final OtbPrepPickingSlipService otbPrepPickingSlipService;
    private final OtbWorkorderDetailService otbWorkorderDetailService;
    private final OtbPrepWorkorderDetailService otbPrepWorkorderDetailService;
    private final OtbPrepWorkorderBinLocationService otbPrepWorkorderBinLocationService;
    private final PickingSlipService pickingSlipService;
    private final BinLocationDetailService binLocationDetailService;
    private final BinLocationDetailLockedService binLocationDetailLockedService;
    private final OtbWorkorderService otbWorkorderService;
    private final ProductVersionService productVersionService;
    private final InventoryReserveService inventoryReserveService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbPrepPickingSlipVO putAway(OtbPrepPickingSlipPutAwayQuery query) {
        OtbPrepPickingSlip prepPickingSlip = otbPrepPickingSlipService.getById(query.getOtbPrepPickingSlipId());
        if (ObjectUtil.isNull(prepPickingSlip)) {
            throw new BusinessException("not found in Prep Picking Slip");
        }
        // 已经上架
        Validate.isTrue(!Objects.equals(OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus(), prepPickingSlip.getOtbPrepPickingSlipStatus()),
                "PickingsSlip: {} current is {} Status", prepPickingSlip.getRefNum(), prepPickingSlip.getOtbPrepPickingSlipStatus()
        );
        int putAwayQty = ObjectUtil.nullToDefault(prepPickingSlip.getPutawayQty(), 0);
        // 上架库存不足
        int currentPutAway = putAwayQty + query.getQty();
        int canPutAwayQty = ObjectUtil.nullToDefault(prepPickingSlip.getAllocatePutawayQty(), 0);
        Validate.isTrue(canPutAwayQty >= query.getQty(), "PutAway qty is not enough");
        // 上架数量不能大于总数
        Validate.isTrue(currentPutAway <= prepPickingSlip.getQty(), "PutAway qty is not enough");

        boolean hasPutAway = currentPutAway == prepPickingSlip.getQty();
        // 设置状态
        prepPickingSlip.setOtbPrepPickingSlipStatus(hasPutAway
                ? OtbPrepPickingSlipStatusEnum.PUT_AWAY.getStatus()
                : prepPickingSlip.getOtbPrepPickingSlipStatus()
        );
        // 已经上架数量
        prepPickingSlip.setPutawayQty(currentPutAway);

        OtbPrepPickingSlipPutAwayContextVO context = new OtbPrepPickingSlipPutAwayContextVO();
        context.setQuery(query);
        context.setPrepPickingSlip(prepPickingSlip);

        // Prep工单上架处理
        this.putAwayPrepWorkOrderList(context);

        // 更新Prep拣货单
        otbPrepPickingSlipService.update(prepPickingSlip);

        // 上架库位
        this.putAwayVirtualBinLocation(context);

        // 更新并设置工单状态
        otbPrepWorkorderService.updateAndSetWorkOrderProcessed(context);

        // 锁定Workorder库位
        this.buildBinLocationLockedList(context);

        // 释放预定锁
        this.putAwayReserveLocked(context);

        // 更新Prep工单详情
        List<OtbPrepPickingSlipDetail> prepPickingSlipDetailList = context.getPrepPickingSlipDetailList();
        Validate.isTrue(otbPrepPickingSlipDetailService.updateBatch(prepPickingSlipDetailList) == prepPickingSlipDetailList.size(),
                "Update PrepPickingSlipDetail Fail"
        );

        // 更新Prep工单详情
        List<OtbPrepWorkorderDetail> putAwayPrepWorkorderDetailList = context.getPutAwayPrepWorkorderDetailList();
        Validate.isTrue(otbPrepWorkorderDetailService.updateBatch(putAwayPrepWorkorderDetailList) == putAwayPrepWorkorderDetailList.size(),
                "Update PrepWorkorderDetail putawayQty Fail"
        );

        // 日志记录
        this.recordPutAwayLog(context);

        return otbPrepPickingSlipService.buildOtbPrepPickingSlipVO(prepPickingSlip);
    }

    /**
     * 上架虚拟库位
     *
     * @param context 上下文
     */
    private void putAwayVirtualBinLocation(OtbPrepPickingSlipPutAwayContextVO context) {
        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 获取产品版本
        ProductVersion productVersion = productVersionService.findLatestProductVersionByProductId(prepPickingSlip.getProductId());
        OtbPrepPickingSlipPutAwayQuery query = context.getQuery();
        // 构建上架参数
        BinLocationDetailContextBO putAwayContext = new BinLocationDetailContextBO();
        putAwayContext.setBinLocationId(prepPickingSlip.getBinLocationId());
        putAwayContext.setProductId(productVersion.getProductId());
        putAwayContext.setProductVersionId(productVersion.getId());
        putAwayContext.setRefNumModel(prepPickingSlip);
        putAwayContext.setInStockQty(query.getQty());

        context.setPutAwayBinLocationDetail(binLocationDetailService.putAwayVirtual(putAwayContext));
    }

    /**
     * 释放预定锁
     *
     * @param context 上下文
     */
    private void putAwayReserveLocked(OtbPrepPickingSlipPutAwayContextVO context) {
        // 获取工单详情
        List<OtbPrepWorkorderPutAwayVO> prepWorkorderPutawayList = context.getPrepWorkorderPutawayList();
        List<Long> workOrderDetailIdList = StreamUtils.distinctMap(prepWorkorderPutawayList, OtbPrepWorkorderPutAwayVO::getOtbWorkorderDetailId);
        List<OtbWorkorderDetail> workorderDetailList = otbWorkorderDetailService.listByIds(workOrderDetailIdList);

        // 不存在释放锁的工单详情
        List<OtbWorkorderDetail> notReserveWorkorderDetailList = workorderDetailList.stream()
                .filter(obj -> ObjectUtil.isNull(obj.getInventoryReserveId()))
                .toList();

        // 存在没有锁的详情
        if (ObjectUtil.isNotEmpty(notReserveWorkorderDetailList)) {
            List<OtbPrepWorkorder> prepWorkorderList = context.getPutAwayPrepWorkOrderList();
            List<Long> workOrderIdList = StreamUtils.distinctMap(prepWorkorderList, OtbPrepWorkorder::getOtbWorkorderId);
            Map<Long, OtbWorkorder> workorderMap = StreamUtils.toMap(otbWorkorderService.listByIds(workOrderIdList), IdModel::getId);

            String workorderMessage = notReserveWorkorderDetailList.stream()
                    .map(obj -> Optional.ofNullable(workorderMap.get(obj.getOtbWorkorderId()))
                            .map(OtbWorkorder::getRefNum)
                            .orElse(String.valueOf(obj.getLineNum())))
                    .collect(Collectors.joining(StringPool.COMMA));

            throw new BusinessException(StringUtil.format("Workorder [{}] InventoryReserveLock lack", workorderMessage));
        }


        Map<Long, Integer> detailPutAwayQtyMap = prepWorkorderPutawayList.stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorderPutAwayVO::getOtbWorkorderDetailId,
                        Collectors.mapping(BasePutAwayVO::getChangePutAwayQty, Collectors.summingInt(Integer::intValue)))
                );

        // 工单详情增加ReserveQty
        List<InventoryReleaseLockedParam> reserveInventoryReleaseList = workorderDetailList
                .stream()
                .filter(obj -> detailPutAwayQtyMap.containsKey(obj.getId()))
                .map(obj -> {
                    InventoryReleaseLockedParam lockedParam = new InventoryReleaseLockedParam();
                    lockedParam.setId(obj.getInventoryReserveId());
                    Integer detailPutAwayQty = detailPutAwayQtyMap.getOrDefault(obj.getId(), 0);
                    lockedParam.setQty(detailPutAwayQty);

                    obj.setFinishReserveQty(obj.getFinishReserveQty() + detailPutAwayQty);
                    Validate.isTrue(obj.getFinishReserveQty() <= obj.getReserveQty(),
                            "Workorder reserveQty [{}] must less than workorder qty [{}]",
                            obj.getFinishReserveQty(), obj.getReserveQty()
                    );
                    return lockedParam;
                })
                .toList();
        // 更新预定数量
        Validate.isTrue(otbWorkorderDetailService.updateBatch(workorderDetailList) == workorderDetailList.size(),
                "Update Workorder reserveQty fail"
        );
        // 更新预定
        inventoryReserveService.putAwayReserveInventory(reserveInventoryReleaseList);
    }

    /**
     * 上架Prep工单
     *
     * @param context 上下文
     */
    private void putAwayPrepWorkOrderList(OtbPrepPickingSlipPutAwayContextVO context) {
        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 处理工单
        List<OtbPrepWorkorder> prepWorkorderList = otbPrepWorkorderService.putAwayListByPrepPickingSlipId(prepPickingSlip.getId());
        // Prep工单详情映射
        Map<Long, List<OtbPrepWorkorderDetail>> prepDetailGroupByWkMap
                = otbPrepWorkorderDetailService.groupByOtbPrepWorkOrderIdList(StreamUtils.distinctMap(prepWorkorderList, IdModel::getId));

        // 全部工单详情
        context.setPrepWorkOrderList(prepWorkorderList);
        context.setPrepDetailGroupByWkMap(prepDetailGroupByWkMap);

        // 分配工单上架数量
        List<OtbPrepWorkorderPutAwayVO> workorderPutawayList = this.allocationWorkOrder(context);

        // 分配工单详情上架数量
        List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList = this.allocationWkDetail(context, workorderPutawayList);

        // 分配拣货单详情上架数量
        this.allocationPsDetail(context, wkDetailPutAwayList);

        // 分配锁释放数量
        this.allocationBinLocationDetailLocked(context, wkDetailPutAwayList);

        // 赋值上架库位
        context.getPutAwayPrepWorkOrderList().forEach(obj -> obj.setBinLocationId(prepPickingSlip.getBinLocationId()));
    }

    /**
     * 构建工单仓库锁
     *
     * @param context 上下文
     */
    private void buildBinLocationLockedList(OtbPrepPickingSlipPutAwayContextVO context) {
        // 工单详情
        List<Long> workorderDetailIdList = context.getPrepWorkorderPutawayList().stream()
                .map(OtbPrepWorkorderPutAwayVO::getOtbWorkorderDetailId)
                .toList();
        List<OtbWorkorderDetail> workorderDetails = otbWorkorderDetailService.listByIds(workorderDetailIdList);

        // 工单
        List<Long> workorderIdList = StreamUtils.distinctMap(workorderDetails, OtbWorkorderDetail::getOtbWorkorderId);
        Map<Long, OtbWorkorder> workorderMap = StreamUtils.toMap(otbWorkorderService.listByIds(workorderIdList), IdModel::getId);

        // 上架的虚拟库位
        BinLocationDetail putAwayBinLocationDetail = context.getPutAwayBinLocationDetail();
        // 工单详情上架数量信息
        Map<Long, Integer> workorderDetailPutAwayQtyMap = context.getPrepWorkorderPutawayList().stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorderPutAwayVO::getOtbWorkorderDetailId,
                        Collectors.mapping(BasePutAwayVO::getChangePutAwayQty, Collectors.summingInt(Integer::intValue))));

        // 锁Workorder BinLocationLocked
        List<BinLocationDetailLocked> lockedList = workorderDetails.stream()
                .filter(detail -> workorderDetailPutAwayQtyMap.containsKey(detail.getId()))
                .map(detail -> {
                    // 构建锁定 库位详情实体对象
                    BinLocationDetailLocked locked = new BinLocationDetailLocked();
                    locked.setId(IdWorker.getId());
                    locked.setBinLocationDetailId(putAwayBinLocationDetail.getId());
                    locked.setBinLocationId(putAwayBinLocationDetail.getBinLocationId());
                    locked.setProductId(putAwayBinLocationDetail.getProductId());
                    locked.setProductVersionId(putAwayBinLocationDetail.getProductVersionId());
                    locked.setQty(workorderDetailPutAwayQtyMap.getOrDefault(detail.getId(), 0));
                    locked.setFinishQty(0);
                    locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
                    locked.setRefTableId(detail.getId());
                    locked.setRefTableName(OtbWorkorderDetail.class.getSimpleName());
                    locked.setRefTableRefNum(String.valueOf(detail.getLineNum()));
                    locked.setRefTableShowName(OtbWorkorder.class.getSimpleName());
                    locked.setRefTableShowRefNum(Optional.ofNullable(workorderMap.get(detail.getOtbWorkorderId()))
                            .map(OtbWorkorder::getRefNum)
                            .orElse(String.valueOf(detail.getOtbWorkorderId())));
                    return locked;
                })
                .toList();

        binLocationDetailLockedService.insertBatch(lockedList);
    }

    /**
     * 分配库位锁，释放，扣减
     *
     * @param context             上下文
     * @param wkDetailPutAwayList 工单上架信息
     */
    private void allocationBinLocationDetailLocked(OtbPrepPickingSlipPutAwayContextVO context,
                                                   List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList) {
        // 工单详情分配仓储信息
        List<OtbPrepWorkorderBinLocation> prepWorkorderBinLocationList = otbPrepWorkorderBinLocationService.listByOtbWorkorderDetailIdList(
                StreamUtils.distinctMap(wkDetailPutAwayList, PrepWorkorderDetailPutAwayVO::getId)
        );

        Map<Long, OtbPrepPickingSlipDetail> detailMap = context.getPrepPickingSlipDetailList()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();

        // 上架释放ReadyToGo锁
        pickingSlipService.putAwayReleaseLocked(wkDetailPutAwayList, prepWorkorderBinLocationList,
                // 根据Prep工单详情分组释放
                OtbPrepWorkorderBinLocation::getOtbPrepWorkorderDetailId,
                // 库位日志获取函数
                (detailId, change) -> {
                    change.setChangeType(StringUtil.format("{} {}",
                            prepPickingSlip.getOtbPrepPickingSlipType(),
                            BinLocationLogEnum.OTB_PREP_PUT_AWAY.getStatus())
                    );
                    String lineNum = Optional.ofNullable(detailMap.get(detailId))
                            .map(OtbPrepPickingSlipDetail::getLineNum)
                            .map(String::valueOf)
                            .orElse(StringPool.EMPTY);

                    // 关联信息
                    RefTableBO refTable = new RefTableBO();
                    refTable.setRefTableId(detailId);
                    refTable.setRefTableRefNum(lineNum);
                    refTable.setRefTableName(OtbPrepPickingSlipDetail.class.getSimpleName());

                    refTable.setRefTableShowRefNum(prepPickingSlip.getRefNum());
                    refTable.setRefTableShowName(OtbPrepPickingSlip.class.getSimpleName());
                    change.setRefTable(refTable);
                }
        );
    }

    /**
     * 分配拣货单详情上架数量
     *
     * @param context             上下文
     * @param wkDetailPutAwayList 工单上架信息
     */
    private void allocationPsDetail(
            OtbPrepPickingSlipPutAwayContextVO context,
            List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList) {

        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // Prep拣货单详情映射
        Map<Long, List<OtbPrepPickingSlipDetail>> prepDetailGroupByPsMap
                = otbPrepPickingSlipDetailService.groupByOtbPrepPickingSlipId(prepPickingSlip.getId());

        List<OtbPrepPickingSlipDetail> psDetailList = prepDetailGroupByPsMap.values()
                .stream()
                .flatMap(Collection::stream)
                .toList();
        // 产品上架分组
        Map<Long, Integer> productPutAwayMap = wkDetailPutAwayList.stream()
                .collect(Collectors.groupingBy(PrepWorkorderDetailPutAwayVO::getProductId,
                        Collectors.summingInt(BasePutAwayVO::getChangePutAwayQty))
                );

        // 按照产品分组分配
        Map<Long, List<OtbPrepPickingSlipDetail>> groupByProductMap = StreamUtils.groupBy(psDetailList, OtbPrepPickingSlipDetail::getProductId);
        Map<Long, OtbPrepPickingSlipDetail> psDetailMap = StreamUtils.toMap(psDetailList, IdModel::getId);
        // 拣货单详情分配
        List<OtbPrepPickingSlipDetail> allocationPsDetailList = new ArrayList<>();

        // 遍历产品上架映射
        for (Map.Entry<Long, Integer> entry : productPutAwayMap.entrySet()) {
            // 复制对应产品的拣货单详情
            List<OtbPickingSlipDetailPutAwayVO> psdList = BeanUtil.copyNew(groupByProductMap.get(entry.getKey()), OtbPickingSlipDetailPutAwayVO.class);

            // // 分配上架数量
            // AllocationUtil.checkAndAllocationPutAwayQty(psdList, entry.getValue());

            psdList.forEach(obj -> obj.setPutawayBeforeQty(obj.getPutawayQty()));

            int remainQty = entry.getValue();

            if (remainQty <= 0) {
                continue;
            }

            // 拣货
            for (OtbPickingSlipDetailPutAwayVO putAwayVO : psdList) {
                if (remainQty <= 0) {
                    break;
                }
                // 当前可以分配的数量
                int currentAllocation = Math.min(putAwayVO.getPickedQty() - putAwayVO.getPutawayQty(), remainQty);

                if (currentAllocation <= 0) {
                    continue;
                }

                //putAwayVO.allocation(currentAllocation + putAwayVO.getPutawayQty());
                putAwayVO.setPutawayQty(currentAllocation + putAwayVO.getPutawayQty());
                remainQty -= currentAllocation;

                // 获取并设置拣货单详情的上架数量
                OtbPrepPickingSlipDetail detail = psDetailMap.get(putAwayVO.getId());
                //todo: 这里是否有问题，直接就复制了 PutAwayQty?
                detail.setPutawayQty(putAwayVO.getPutawayQty());
                allocationPsDetailList.add(detail);
            }
            if (remainQty != 0) {
                throw new ImpossibleException(StringUtil.format("allocationPsDetail Error, psdList: {}, remainQty: {}", psdList, remainQty));
            }

            // // 遍历分配后的列表
            // for (OtbPickingSlipDetailPutAwayVO putAway : psdList) {
            //     // 只处理有上架数量变化的项
            //     if (putAway.getChangePutAwayQty() > 0) {
            //         // 获取并设置拣货单详情的上架数量
            //         OtbPrepPickingSlipDetail detail = psDetailMap.get(putAway.getId());
            //         detail.setPutawayQty(putAway.getPutawayQty());
            //         allocationPsDetailList.add(detail);
            //     }
            // }
        }

        // 绑定到上下文
        context.setPrepPickingSlipDetailList(allocationPsDetailList);
    }

    /**
     * 分配工单详情上架数量
     *
     * @param workorderPutawayList 工单上架信息
     * @return /
     */
    private List<PrepWorkorderDetailPutAwayVO> allocationWkDetail(
            OtbPrepPickingSlipPutAwayContextVO context,
            List<OtbPrepWorkorderPutAwayVO> workorderPutawayList) {
        Map<Long, List<OtbPrepWorkorderDetail>> prepDetailGroupByWkMap = context.getPrepDetailGroupByWkMap();
        // 使用for循环替代Stream API
        List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList = new ArrayList<>();
        for (OtbPrepWorkorderPutAwayVO wk : workorderPutawayList) {
            List<OtbPrepWorkorderDetail> prepWorkorderDetails = prepDetailGroupByWkMap.get(wk.getId());
            if (prepWorkorderDetails == null) {
                continue;
            }
            List<PrepWorkorderDetailPutAwayVO> copiedDetails = BeanUtil.copyNew(prepWorkorderDetails, PrepWorkorderDetailPutAwayVO.class);
            for (PrepWorkorderDetailPutAwayVO putAway : copiedDetails) {
                // detail.putAwayQty = detail.qty * header.putAwayQty / header.qty
                int allocationQty = putAway.getQty() * wk.getChangePutAwayQty() / wk.getQty();
                AllocationUtil.checkAndAllocationPutAwayQty(Collections.singletonList(putAway), allocationQty);
                wkDetailPutAwayList.add(putAway);
            }
        }

        // 工单详情映射
        Map<Long, OtbPrepWorkorderDetail> wkDetailMap = prepDetailGroupByWkMap.values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        // 上架数量赋值
        List<Pair<OtbPrepWorkorderDetail, PrepWorkorderDetailPutAwayVO>> pairList = wkDetailPutAwayList.stream()
                .filter(wkDetail -> wkDetail.getChangePutAwayQty() > 0)
                // 赋值工单详情上架数量
                .map(wkDetail -> {
                    OtbPrepWorkorderDetail detail = wkDetailMap.get(wkDetail.getId());
                    detail.setPutawayQty(wkDetail.getPutawayQty());
                    return Pair.of(detail, wkDetail);
                })
                .toList();

        // 绑定到上下文
        context.setPutAwayPrepWorkorderDetailList(StreamUtils.distinctMap(pairList, Pair::getKey));

        // 返回工单详情上架信息
        return pairList.stream()
                .map(Pair::getValue)
                // Prep Convert 不参与后续拣货
                .filter(obj -> !Objects.equals(obj.getPrepWorkorderDetailType(), PrepWorkOrderDetailTypeEnum.PREPCONVERT.getStatus()))
                .toList();
    }

    /**
     * 分配工单上架数量
     *
     * @param context 上下文
     * @return /
     */
    private List<OtbPrepWorkorderPutAwayVO> allocationWorkOrder(OtbPrepPickingSlipPutAwayContextVO context) {
        List<OtbPrepWorkorder> prepWorkorderList = context.getPrepWorkOrderList();
        Map<Long, List<OtbPrepWorkorderDetail>> prepDetailGroupByWkMap = context.getPrepDetailGroupByWkMap();
        // 工单上架信息
        List<OtbPrepWorkorderPutAwayVO> workorderPutawayList = prepWorkorderList.stream()
                .map(obj -> {
                    OtbPrepWorkorderPutAwayVO putAway = BeanUtil.copyNew(obj, OtbPrepWorkorderPutAwayVO.class);
                    int workorderPickedQty = prepDetailGroupByWkMap
                            .getOrDefault(obj.getId(), Collections.emptyList())
                            .stream()
                            // header.pickedQty = header.qty * detail.pickedQty / detail.qty
                            .mapToInt(detail -> obj.getQty() * detail.getPickedQty() / detail.getQty())
                            .min()
                            .orElse(0);
                    // 工单没有拣货数量，为了符合通用计算逻辑
                    putAway.setPickedQty(workorderPickedQty);
                    return putAway;
                })
                .toList();
        // 工单分配
        AllocationUtil.checkAndAllocationPutAwayQty(workorderPutawayList, context.getQuery().getQty());
        // 工单映射
        Map<Long, OtbPrepWorkorder> prepWorkorderMap = StreamUtils.toMap(prepWorkorderList, IdModel::getId);

        // 赋值上架数量
        List<Pair<OtbPrepWorkorder, OtbPrepWorkorderPutAwayVO>> workorderPairList = workorderPutawayList.stream()
                .filter(wk -> wk.getChangePutAwayQty() > 0)
                // 赋值工单上架数量
                .map(wk -> {
                    OtbPrepWorkorder prepWorkorder = prepWorkorderMap.get(wk.getId());
                    prepWorkorder.setPutawayQty(wk.getPutawayQty());
                    prepWorkorder.setOtbPrepWorkorderStatus(Objects.equals(prepWorkorder.getQty(), prepWorkorder.getPutawayQty())
                            // Prep工单 Processed
                            ? OtbPrepWorkOrderEnum.PROCESSED.getStatus()
                            : prepWorkorder.getOtbPrepWorkorderStatus());
                    return Pair.of(prepWorkorder, wk);
                })
                .toList();

        // 绑定到上下文中
        context.setPutAwayPrepWorkOrderList(StreamUtils.distinctMap(workorderPairList, Pair::getKey));
        context.setPrepWorkorderPutawayList(StreamUtils.distinctMap(workorderPairList, Pair::getValue));

        // 返回工单上架信息
        return StreamUtils.distinctMap(workorderPairList, Pair::getValue);
    }


    /**
     * 记录上架日志
     *
     * @param context 上下文
     */
    private void recordPutAwayLog(OtbPrepPickingSlipPutAwayContextVO context) {
        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        int putAwayQty = context.getQuery().getQty();

        // Prep拣货单: PutAway 日志
        OtcPutAwayLogVO putAwayLog = new OtcPutAwayLogVO(prepPickingSlip.getProductId(), prepPickingSlip.getBinLocationId(), putAwayQty);

        // PutAway 上架 库位 产品 数量
        OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, OtbPrepPickingSlipStatusEnum.PUT_AWAY.getStatus(),
                JsonUtil.toJson(putAwayLog), null, BaseTypeLogEnum.OPERATION.getType()
        );

        // PutAway Completed 上架完成
        if (Objects.equals(prepPickingSlip.getOtbPrepPickingSlipStatus(), OtbPrepPickingSlipStatusEnum.PUT_AWAY.getStatus())) {
            OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, PickingSlipLogConstant.PUT_AWAY_COMPLETED_STATUS, null);
        }

        // Prep工单: Processing -> Processed 日志
        context.getPutAwayPrepWorkOrderList()
                .stream()
                .filter(obj -> Objects.equals(obj.getOtbPrepWorkorderStatus(), OtbPrepWorkOrderEnum.PROCESSED.getStatus()))
                .forEach(OtbPrepWorkorderAuditLogHelper::recordLog);

        // 工单: workorder_prep_status Processed
        OtbWorkorderAuditLogHelper.recordLog(context.getWorkorderList(), WorkorderLogConstant.PREP_WORK_ORDER_PROCESSED_DESCRIPTION, null, null);
    }

}
