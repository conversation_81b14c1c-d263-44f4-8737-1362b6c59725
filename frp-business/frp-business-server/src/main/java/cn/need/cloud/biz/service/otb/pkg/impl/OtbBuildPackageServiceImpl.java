package cn.need.cloud.biz.service.otb.pkg.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPackagePalletTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPackageStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PackageLabelTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.PaperTypeEnum;
import cn.need.cloud.biz.config.ShipConfig;
import cn.need.cloud.biz.model.bo.common.CommonShipRespBO;
import cn.need.cloud.biz.model.bo.otb.OtbBuildPackageContextBo;
import cn.need.cloud.biz.model.bo.otb.ShipAddressBO;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.otb.create.pkg.OtbPackageCreateParam;
import cn.need.cloud.biz.model.vo.base.BasePackageVO;
import cn.need.cloud.biz.model.vo.base.BasePackedVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbBuildPackageVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageDetailVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageLabelVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentLabelVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPackageAuditLogHelper;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipDetailService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.pkg.OtbBuildPackageService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageDetailService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageLabelService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.service.ship.CommonClientService;
import cn.need.cloud.biz.service.ship.SsccClientService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.TimeUtils;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.ship.client.dto.base.*;
import cn.need.cloud.ship.client.dto.common.CommonPackageResDTO;
import cn.need.cloud.ship.client.dto.common.CommonShipCreateReqDTO;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.CollectionUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ImpossibleException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static cn.need.framework.common.core.bean.BeanUtil.copyNew;

/**
 * <p>
 * OTB包裹build service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Service
public class OtbBuildPackageServiceImpl implements OtbBuildPackageService {

    @Resource
    private OtbWorkorderService otbWorkorderService;
    @Resource
    private OtbRequestService otbRequestService;
    @Resource
    private OtbPickingSlipService otbPickingSlipService;
    @Resource
    private OtbPickingSlipDetailService otbPickingSlipDetailService;
    @Resource
    private OtbPackageDetailService otbPackageDetailService;
    @Resource
    private OtbWorkorderDetailService otbWorkorderDetailService;
    @Resource
    private OtbPackageLabelService otbPackageLabelService;
    @Resource
    private OtbPackageService otbPackageService;

    @Resource
    private OtbShipmentService otbShipmentService;
    @Resource
    private SsccClientService ssccClientService;
    @Resource
    private ShipConfig shipConfig;
    @Resource
    private CommonClientService commonClientService;
    @Resource
    private FileStringUploadService fileStringUploadService;
    @Resource
    private OtbRoutingInstructionService otbRoutingInstructionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OtbBuildPackageVO> build(OtbPackageCreateParam param) {
        //获取拣货单信息
        OtbBuildPackageContextBo contextBo = new OtbBuildPackageContextBo();
        contextBo.setParam(param);
        //构建上下文信息
        filterBuildContextBo(param, contextBo);

        //check pickingslip ShipType,只允许ByWarehouse 才能BuildPackage
        if (!ShipTypeEnum.BY_WAREHOUSE.equals(contextBo.getOtbPickingSlip().getShipType())) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, StringUtil.format("Only support ByWarehouse shipType build packages,{} is {}", contextBo.getOtbPickingSlip().refNumLog(), contextBo.getOtbPickingSlip().getShipType().getCode())));
        }

        //更新拣货单状态
        otbPickingSlipService.checkPackedQty(contextBo);
        //分配打包数量
        allocationPackQty(contextBo);
        //构建包裹
        List<OtbPackage> otbPackageList = initOtbPackage(contextBo);
        //持久化包裹
        otbPackageService.insertBatch(otbPackageList);
        //记录日志
        OtbPackageAuditLogHelper.recordLog(
                otbPackageList,
                contextBo.getOtbPickingSlip().toLog()
        );
        //构建包裹详情
        otbPackageDetailService.generateDetail(contextBo);
        //持久化工单，拣货单打包数量
        otbWorkorderDetailService.updateBatch(contextBo.getOtbWorkorderDetailList());
        otbPickingSlipDetailService.updateBatch(contextBo.getOtbPickingSlipDetailList());
        //生成打包标签
        otbPackageLabelService.generatePackageLabel(contextBo);
        //返回打印信息
        return buildOtbBuildPackageVO(contextBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbShipmentLabelVO buildPackageShippingLabel(Long shipmentId, Long packageId) {
        //获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(shipmentId);
        //获取包裹
        OtbPackage otbPackage = otbPackageService.getById(packageId);
        Validate.isTrue(StringUtil.equals(otbPackage.getOtbPackageStatus(), OtbPackageStatusEnum.CHANNEL_CONFIRM.getCode()),
                StringUtil.format("Current status is {} Can not buildPackageShippingLabel", otbPackage.getOtbPackageStatus()));
        //获取请求单
        OtbRequest otbRequest = otbRequestService.getById(otbShipment.getOtbRequestId());
        ShipAddressBO shipAddressBO = BeanUtil.copyNew(otbRequest, ShipAddressBO.class);
        //copy address
        BeanUtil.copy(shipAddressBO, otbPackage);
        OtbPackageLabel otbPackageLabel = otbPackageLabelService.listByPackageId(packageId)
                .stream()
                .filter(item -> StringUtil.equals(item.getLabelType(), PackageLabelTypeEnum.SHIPPING_LABEL.getType()) &&
                        !StringUtil.equals(item.getPrintStatus(), PrintStatusEnum.SUCCESS.getStatus()))
                .findFirst()
                .map(item -> BeanUtil.copyNew(item, OtbPackageLabel.class))
                .orElse(null);
        //判断是否生成了shipmentLabel
        if (ObjectUtil.isEmpty(otbPackageLabel)) {
            //生成label
            otbPackageLabel = buildPackageLabel(otbPackage, otbRequest, otbShipment.getId());
            //持久化
            otbPackageLabelService.insert(otbPackageLabel);
        }
        //更新包裹状态
        otbPackage.setOtbPackageStatus(OtbPackageStatusEnum.BUILD_SHIPMENT_LABEL.getCode());
        otbPackageService.update(otbPackage);
        //记录日志
        OtbPackageAuditLogHelper.recordLog(otbPackage);
        //返回发货单标签
        return BeanUtil.copyNew(otbPackageLabel, OtbShipmentLabelVO.class);
    }

    /**
     * 构建上下文信息
     *
     * @param param     前端参数
     * @param contextBo 上下文信息对象
     */
    private void filterBuildContextBo(OtbPackageCreateParam param, OtbBuildPackageContextBo contextBo) {
        //获取入库工单信息
        OtbWorkorder otbWorkorder = otbWorkorderService.getByPickSlipId(param.getPickingSlipId());
        //获取打包产品信息
        BasePackageVO basePackageVO = param.getFullPackageDetailList()
                .stream()
                .findFirst()
                .orElse(new BasePackageVO());
        List<OtbWorkorderDetailVO> otbWorkorderDetailList = otbWorkorderDetailService.listByOtbWorkorderId(otbWorkorder.getId())
                .stream()
                .filter(item -> StringUtil.equals(basePackageVO.getProductBarcode(), item.getDetailSnapshotProductBarcode()) &&
                        StringUtil.equals(basePackageVO.getProductChannelSku(), item.getDetailSnapshotProductChannelSku()) &&
                        ObjectUtil.equal(basePackageVO.getProductId(), item.getProductId()))
                .toList();
        //获取出去请求单
        OtbRequest otbRequest = otbRequestService.getById(otbWorkorder.getOtbRequestId());
        //填充拣货单
        OtbPickingSlip otbPickingSlip = otbPickingSlipService.getById(param.getPickingSlipId());
        List<OtbPickingSlipDetailVO> otbPickingSlipDetailList = otbPickingSlipDetailService.listByPickSlipId(param.getPickingSlipId())
                .stream()
                .filter(item -> StringUtil.equals(basePackageVO.getProductBarcode(), item.getProductBarcode()) &&
                        StringUtil.equals(basePackageVO.getProductChannelSku(), item.getProductChannelSku()) &&
                        ObjectUtil.equal(basePackageVO.getProductId(), item.getProductId()))
                .toList();
        //填充工单，拣货单，请求单信息到上下文
        contextBo.setOtbRequest(otbRequest);
        contextBo.setOtbWorkorder(otbWorkorder);
        contextBo.setOtbPickingSlip(otbPickingSlip);
        contextBo.setOtbPickingSlipDetailList(BeanUtil.copyNew(otbPickingSlipDetailList, OtbPickingSlipDetail.class));
        contextBo.setOtbWorkorderDetailList(BeanUtil.copyNew(otbWorkorderDetailList, OtbWorkorderDetail.class));
        //计算打包数量
        List<BasePackageVO> fullPackageDetailList = param.getFullPackageDetailList();
        List<BasePackageVO> tailPackageDetailList = param.getTailPackageDetailList();
        BasePackageVO fullPackageVO = fullPackageDetailList.stream().findFirst().orElse(new BasePackageVO());
        BasePackageVO tailPackageVO = tailPackageDetailList.stream().findFirst().orElse(new BasePackageVO());
        //获取
        contextBo.setThisPackQty(fullPackageVO.getQty() * param.getFullPackageQty() + tailPackageVO.getQty());
    }

    /**
     * 分配打包数量
     *
     * @param contextBo 上下文信息
     */
    private void allocationPackQty(OtbBuildPackageContextBo contextBo) {
        //分配拣货单打包数量
        List<OtbPickingSlipDetail> otbPickingSlipDetailList = contextBo.getOtbPickingSlipDetailList();
        List<BasePackedVO> pickingSlipList = otbPickingSlipDetailList.stream().map(item -> {
            BasePackedVO basePackedVO = copyNew(item, BasePackedVO.class);
            basePackedVO.setPickedQty(item.getPickedQty());
            return basePackedVO;
        }).toList();

        // AllocationUtil.checkAndAllocationQty(pickingSlipList, contextBo.getThisPackQty());
        //todo:去掉这种分配逻辑，直接使用原本的即可,不需要转换
        {
            int remainQty = contextBo.getThisPackQty();
            if (remainQty <= 0) {
                return;
            }
            for (BasePackedVO pickingSlip : pickingSlipList) {
                if (remainQty <= 0) {
                    break;
                }

                if (remainQty <= 0) {
                    break;
                }
                // 当前可以分配的数量
                int currentAllocation = Math.min(pickingSlip.total() - pickingSlip.allocated(), remainQty);
                pickingSlip.allocation(currentAllocation + pickingSlip.allocated());
                remainQty -= currentAllocation;
            }
            if (remainQty != 0) {
                throw new ImpossibleException(StringUtil.format("allocationPackQty Error, pickingSlipList: {}, remainQty: {}", pickingSlipList, remainQty));
            }
        }
        //分配工单打包数量
        List<OtbWorkorderDetail> otbWorkorderDetailList = contextBo.getOtbWorkorderDetailList();
        List<BasePackedVO> workOrderList = otbWorkorderDetailList.stream().map(item -> {
            BasePackedVO basePackedVO = copyNew(item, BasePackedVO.class);
            basePackedVO.setPickedQty(item.getPickedQty());
            return basePackedVO;
        }).toList();
        //AllocationUtil.checkAndAllocationQty(workOrderList, contextBo.getThisPackQty());

        //todo:去掉这种分配逻辑，直接使用原本的即可,不需要转换

        {
            int remainQty = contextBo.getThisPackQty();
            if (remainQty <= 0) {
                return;
            }
            for (BasePackedVO workorder : workOrderList) {
                if (remainQty <= 0) {
                    break;
                }

                if (remainQty <= 0) {
                    break;
                }
                // 当前可以分配的数量
                int currentAllocation = Math.min(workorder.total() - workorder.allocated(), remainQty);
                workorder.allocation(currentAllocation + workorder.allocated());
                remainQty -= currentAllocation;
            }
            if (remainQty != 0) {
                throw new ImpossibleException(StringUtil.format("allocationPackQty Error, workOrderList: {}, remainQty: {}", workOrderList, remainQty));
            }
        }

        //根据id映射拣货单分配打包对象
        List<BasePackedVO> list = CollectionUtil.contact(pickingSlipList, workOrderList);
        Map<Long, Integer> packQtyMap = ObjectUtil.toMap(list, BasePackedVO::getId, BasePackedVO::getPackedQty);
        //更新拣货单
        otbPickingSlipDetailList.forEach(item -> item.setPackedQty(packQtyMap.getOrDefault(item.getId(), 0)));
        //更新工单
        otbWorkorderDetailList.forEach(item -> item.setPackedQty(packQtyMap.getOrDefault(item.getId(), 0)));
        contextBo.setOtbWorkorderDetailList(otbWorkorderDetailList);
        contextBo.setOtbPickingSlipDetailList(otbPickingSlipDetailList);
    }

    /**
     * 构建包裹VO对象
     *
     * @param contextBo 上下文对象
     * @return 返回包裹VO对象
     */
    private List<OtbBuildPackageVO> buildOtbBuildPackageVO(OtbBuildPackageContextBo contextBo) {
        //获取包裹集合
        List<OtbBuildPackageVO> otbBuildPackageList = copyNew(contextBo.getOtbPackageList(), OtbBuildPackageVO.class);
        //获取包裹详情
        List<OtbPackageDetail> otbPackageDetailList = otbPackageDetailService.listByOtbPackageId(contextBo.getOtbPackageList().stream().map(OtbPackage::getId).toList());
        //根据包裹id映射包裹
        List<OtbPackageDetailVO> packageDetailList = copyNew(otbPackageDetailList, OtbPackageDetailVO.class);
        Map<Long, List<OtbPackageDetailVO>> detailMap = ObjectUtil.toMapList(packageDetailList, OtbPackageDetailVO::getOtbPackageId);
        //获取label标签集合
        List<OtbPackageLabelVO> otbPackageLabelList = contextBo.getOtbPackageLabelList();
        //根据包裹id映射label标签集合
        Map<Long, List<OtbPackageLabelVO>> labelMap = ObjectUtil.toMapList(otbPackageLabelList, OtbPackageLabelVO::getOtbPackageId);
        //遍历包裹集合
        otbBuildPackageList.forEach(item -> {
            //填充包裹详情
            item.setOtbPackageDetailList(detailMap.get(item.getId()));
            //填充label标签集合
            item.setOtbPackageLabelList(labelMap.get(item.getId()));
            //填充工单信息
            OtbWorkorder otbWorkorder = contextBo.getOtbWorkorder();
            item.setOtbWorkorderRefNum(otbWorkorder.getRefNum());
            //填充请求单信息
            OtbRequest otbRequest = contextBo.getOtbRequest();
            item.setOtbRequestOfRefNum(otbRequest.getRefNum());
            //填充拣货单信息
            OtbPickingSlip otbPickingSlip = contextBo.getOtbPickingSlip();
            item.setOtbPickingSlipRefNum(otbPickingSlip.getRefNum());
        });
        //填充仓库
        WarehouseCacheUtil.filledWarehouse(otbBuildPackageList);
        return otbBuildPackageList;
    }

    /**
     * 初始化包裹
     *
     * @param contextBo 创建otb包裹信息
     * @return 包裹集合
     */
    private List<OtbPackage> initOtbPackage(OtbBuildPackageContextBo contextBo) {
        OtbPackageCreateParam param = contextBo.getParam();
        //存在满箱
        Stream<OtbPackage> fullStream = buildStream(contextBo, OtbPackageStatusEnum.FULL.getCode(), param.getFullPackageQty());
        //存在半箱
        //若没有半箱
        BasePackageVO basePackageVO = param.getTailPackageDetailList().stream().findFirst().orElse(new BasePackageVO());
        Stream<OtbPackage> tailStream = Stream.empty();
        if (ObjectUtil.notEqual(basePackageVO.getQty(), 0)) {
            tailStream = buildStream(contextBo, OtbPackageStatusEnum.TAIL.getCode(), param.getTailPackageDetailList().size());
        }
        //合并包裹流
        List<OtbPackage> list = Stream.concat(fullStream, tailStream).toList();
        contextBo.setOtbPackageList(list);
        //返回包裹集合
        return list;
    }

    /**
     * 获取包裹流
     *
     * @param contextBo 上下文信息
     * @return 返回包裹流
     */
    private Stream<OtbPackage> buildStream(OtbBuildPackageContextBo contextBo, String type, Integer length) {
        //获取前端参数
        OtbPackageCreateParam param = contextBo.getParam();
        //返回包裹流
        return IntStream.range(0, length)
                .mapToObj(item -> {
                    OtbPackage otbPackage = BeanUtil.copyNew(param, OtbPackage.class);
                    //填充包裹状态
                    otbPackage.setOtbPackageType(type);
                    //填充打托类型
                    otbPackage.setOtbPalletType(OtbPackagePalletTypeEnum.NONE.getType());
                    //填充包裹到上下文信息
                    contextBo.setOtbPackage(otbPackage);
                    //填充包裹信息
                    fillOtbPackage(contextBo);
                    //记录日志
                    OtbPackageAuditLogHelper.recordLog(otbPackage, contextBo.getOtbPickingSlip().toLog());
                    return otbPackage;
                });
    }

    /**
     * 填充包裹信息
     *
     * @param contextBo 上下文信息
     */
    private void fillOtbPackage(OtbBuildPackageContextBo contextBo) {
        //包裹
        OtbPackage otbPackage = contextBo.getOtbPackage();
        //请求单
        OtbRequest otbRequest = contextBo.getOtbRequest();
        //拣货单
        OtbPickingSlip otbPickingSlip = contextBo.getOtbPickingSlip();
        //工单
        OtbWorkorder otbWorkorder = contextBo.getOtbWorkorder();
        // ssccNum
        otbPackage.setSsccNum(ssccClientService.getSsccNum(shipConfig.getSsccPrefix()));
        // sscc shortd
        // otbPackage.setShortSsccNum(ssccClientService.getShortSsccNum(shipConfig.getSsccPrefix()));
        //填充工单id
        otbPackage.setOtbWorkorderId(otbWorkorder.getId());
        //请求单id
        otbPackage.setOtbRequestId(otbWorkorder.getOtbRequestId());
        //仓库id
        otbPackage.setWarehouseId(otbRequest.getWarehouseId());
        //orderNum
        otbPackage.setOrderNum(otbRequest.getOrderNum());
        //拣货单id
        otbPackage.setOtbPickingSlipId(otbWorkorder.getOtbPickingSlipId());
        //包裹状态
        otbPackage.setOtbPackageStatus(OtbPackageStatusEnum.NEW.getCode());
        //copy 发货地址
        ShipAddressBO shipAddressBO = copyNew(otbRequest, ShipAddressBO.class);
        BeanUtil.copy(shipAddressBO, otbPackage);
        //填充包裹流水号
        otbPackage.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTB_PACKAGE.getCode()));
        //拣货位置
        otbPackage.setStation(otbPickingSlip.getPickToStation());
    }

    /**
     * 构建发货单标签
     *
     * @param otbPackage 包裹对象
     * @return 返回发货单标签对象
     */
    private OtbPackageLabel buildPackageLabel(OtbPackage otbPackage, OtbRequest otbRequest, Long shipmentId) {
        CommonShipRespBO respBO = commonClientService.createShipment(buildShippingLabel(otbPackage, otbRequest, shipmentId));
        CommonPackageResDTO resDTO = respBO
                .getPackages()
                .stream()
                .findFirst()
                .orElseGet(CommonPackageResDTO::new);

        //填充快递单号
        otbPackage.setTrackingNum(resDTO.getTrackingNum());

        OtbPackageLabel otbPackageLabel = new OtbPackageLabel();
        otbPackageLabel.setId(IdWorker.getId());
        otbPackageLabel.setLineNum(1);
        otbPackageLabel.setLabelType(PackageLabelTypeEnum.SHIPPING_LABEL.getType());
        otbPackageLabel.setLabelRefNum(resDTO.getTrackingNum());
        otbPackageLabel.setLabelRawData(resDTO.getEncodedLabel());
        otbPackageLabel.setRawDataType(FileDataTypeEnum.BASE_64_STRING_PNG.getType());
        otbPackageLabel.setPaperType(PaperTypeEnum.LABEL_4X6.getStatus());
        otbPackageLabel.setOtbPackageId(otbPackage.getId());
        otbPackageLabel.setFileIdRawDataType(FileDataTypeEnum.BASE_64_STRING_PNG.getType());
        otbPackageLabel.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        //文件上传并填充LabelRawData
        fileStringUploadService.uploadLabelBatch(Collections.singletonList(otbPackageLabel));
        //返回label信息
        return otbPackageLabel;
    }

    private CommonShipCreateReqDTO buildShippingLabel(OtbPackage otbPackage, OtbRequest otbRequest, Long shipmentId) {
        //获取渠道确认单
        OtbRoutingInstruction instruction = otbRoutingInstructionService.getByShipmentId(shipmentId);

        if (ObjectUtil.isEmpty(instruction.getShipApiProfileRefNum())) {
            throw new BusinessException("Invalid shipApiProfileRefNum, Can not found in OtbRoutingInstruction" + instruction.getShipApiProfileRefNum());
        }

        CommonShipCreateReqDTO reqDTO = new CommonShipCreateReqDTO();
        reqDTO.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getEncodeFormat());
        reqDTO.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        reqDTO.setProfileRefNum(instruction.getShipApiProfileRefNum());
        reqDTO.setAppId(String.valueOf(otbPackage.getTenantId()));

        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setShipCarrier(instruction.getShipCarrier());
        baseInfoDTO.setShipMethod(instruction.getShipMethod());
        baseInfoDTO.setRequestRefNum(otbRequest.getRequestRefNum());
        fillAddress(baseInfoDTO, instruction);
        baseInfoDTO.setShipDate(TimeUtils.now());

        BasePackageDTO basePackageDTO = new BasePackageDTO();
        basePackageDTO.setPackageRefNum(otbPackage.getRefNum());
        basePackageDTO.setSignatureType(instruction.getSignatureType());

        BaseInsuranceDTO baseInsuranceDTO = new BaseInsuranceDTO();
        baseInsuranceDTO.setAmount(instruction.getInsuranceAmountAmount());
        baseInsuranceDTO.setCurrency("USD");
        boolean b = ObjectUtil.isNotEmpty(instruction.getInsuranceAmountAmount()) &&
                ObjectUtil.isNotEmpty(instruction.getInsuranceAmountCurrency());
        basePackageDTO.setInsuranceAmount(b ? baseInsuranceDTO : null);


        BaseShipSizeDTO baseShipSizeDTO = new BaseShipSizeDTO();
        baseShipSizeDTO.setHeight(otbPackage.getCartonSizeHeight());
        baseShipSizeDTO.setLength(otbPackage.getCartonSizeLength());
        baseShipSizeDTO.setWidth(otbPackage.getCartonSizeWidth());
        baseShipSizeDTO.setDimensionUnit("IN");
        baseShipSizeDTO.setWeight(otbPackage.getCartonSizeWeight());
        baseShipSizeDTO.setWeightUnit("LB");

        basePackageDTO.setShipSize(baseShipSizeDTO);

        reqDTO.setPackages(Collections.singletonList(basePackageDTO));

        reqDTO.setBase(baseInfoDTO);
        return reqDTO;
    }

    /**
     * 填充地址信息
     *
     * @param baseInfoDTO 地址信息
     * @param instruction 请求单
     */
    private void fillAddress(BaseInfoDTO baseInfoDTO, OtbRoutingInstruction instruction) {
        BaseAddressDTO baseAddressDTO = new BaseAddressDTO();
        baseAddressDTO.setName(instruction.getShipToAddressName());
        baseAddressDTO.setAddr1(instruction.getShipToAddressAddr1());
        baseAddressDTO.setAddr2(instruction.getShipToAddressAddr2());
        baseAddressDTO.setAddr3(instruction.getShipToAddressAddr3());
        baseAddressDTO.setCity(instruction.getShipToAddressCity());
        baseAddressDTO.setCountry(instruction.getShipToAddressCountry());
        baseAddressDTO.setEmail(instruction.getShipToAddressEmail());
        baseAddressDTO.setPhone(instruction.getShipToAddressPhone());
        baseAddressDTO.setState(instruction.getShipToAddressState());
        baseAddressDTO.setZipCode(instruction.getShipToAddressZipCode());
        baseAddressDTO.setIsResidential(instruction.getShipToAddressIsResidential());
        baseAddressDTO.setCompany(instruction.getShipToAddressCompany());
        baseInfoDTO.setShipToAddress(baseAddressDTO);
        BaseAddressDTO baseAddressDTO1 = new BaseAddressDTO();
        baseAddressDTO1.setName(instruction.getShipFromAddressName());
        baseAddressDTO1.setAddr1(instruction.getShipFromAddressAddr1());
        baseAddressDTO1.setAddr2(instruction.getShipFromAddressAddr2());
        baseAddressDTO1.setAddr3(instruction.getShipFromAddressAddr3());
        baseAddressDTO1.setCity(instruction.getShipFromAddressCity());
        baseAddressDTO1.setCountry(instruction.getShipFromAddressCountry());
        baseAddressDTO1.setEmail(instruction.getShipFromAddressEmail());
        baseAddressDTO1.setPhone(instruction.getShipFromAddressPhone());
        baseAddressDTO1.setState(instruction.getShipFromAddressState());
        baseAddressDTO1.setZipCode(instruction.getShipFromAddressZipCode());
        baseAddressDTO1.setIsResidential(instruction.getShipFromAddressIsResidential());
        baseAddressDTO1.setCompany(instruction.getShipFromAddressCompany());
        baseInfoDTO.setShipFromAddress(baseAddressDTO1);
    }
}
