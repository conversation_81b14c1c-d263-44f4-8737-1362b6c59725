@echo off
echo ========================================
echo 运行 BinLocationLog 相关测试
echo ========================================

cd /d "D:\NEWCODES\frp-api"

echo.
echo 1. 运行 OtcPackageServiceImplTest 测试...
echo ----------------------------------------
call mvn test -Dtest=OtcPackageServiceImplTest -pl frp-business/frp-business-server

echo.
echo 2. 运行 BinLocationLogIntegrationTest 测试...
echo ----------------------------------------
call mvn test -Dtest=BinLocationLogIntegrationTest -pl frp-business/frp-business-server

echo.
echo 3. 运行所有 BinLocationLog 相关测试...
echo ----------------------------------------
call mvn test -Dtest="*BinLocation*Test" -pl frp-business/frp-business-server

echo.
echo ========================================
echo 测试完成
echo ========================================
pause
