package cn.need.cloud.biz.service.log;

import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * BinLocationLog 数据修复服务（修正版）
 * 
 * 修复目标：
 * 1. 修复 inventoryaudit 操作的 beforeqty 数据准确性
 * 2. 重建链条连续性，确保 afterQty(n) = beforeQty(n+1)
 * 3. 保持同位置操作的完整 source 和 dest 信息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BinLocationLogDataFixService {

    private final BinLocationLogService binLocationLogService;

    /**
     * 执行完整的数据修复流程
     */
    @Transactional(rollbackFor = Exception.class)
    public DataFixResult executeFullDataFix(LocalDateTime startTime) {
        log.info("开始执行 BinLocationLog 数据修复，起始时间: {}", startTime);
        
        DataFixResult result = new DataFixResult();
        result.setStartTime(startTime);
        result.setFixStartTime(LocalDateTime.now());
        
        try {
            // 步骤1: 分析问题数据
            DataAnalysisResult analysis = analyzeDataProblems(startTime);
            result.setBeforeFixAnalysis(analysis);
            log.info("修复前数据分析: 总记录数={}, 链条断裂数={}, 数学错误数={}", 
                    analysis.getTotalRecords(), analysis.getBrokenChainCount(), analysis.getMathErrorCount());
            
            // 步骤2: 修复 inventoryaudit 数据
            int auditFixCount = fixInventoryAuditData(startTime);
            result.setAuditFixCount(auditFixCount);
            
            // 步骤3: 重建链条连续性
            int chainFixCount = rebuildChainContinuity(startTime);
            result.setChainFixCount(chainFixCount);
            
            // 步骤4: 验证修复结果
            DataAnalysisResult afterAnalysis = analyzeDataProblems(startTime);
            result.setAfterFixAnalysis(afterAnalysis);
            
            result.setFixEndTime(LocalDateTime.now());
            result.setSuccess(true);
            
            log.info("数据修复完成: audit修复={}, 链条修复={}, 剩余问题: 链条断裂={}, 数学错误={}", 
                    auditFixCount, chainFixCount, afterAnalysis.getBrokenChainCount(), afterAnalysis.getMathErrorCount());
            
        } catch (Exception e) {
            log.error("数据修复失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            throw e;
        }
        
        return result;
    }

    /**
     * 分析数据问题
     */
    public DataAnalysisResult analyzeDataProblems(LocalDateTime startTime) {
        log.info("开始分析数据问题");
        
        QueryWrapper<BinLocationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("create_time", startTime);
        
        List<BinLocationLog> allLogs = binLocationLogService.list(queryWrapper);
        
        DataAnalysisResult result = new DataAnalysisResult();
        result.setTotalRecords(allLogs.size());
        
        // 分析链条断裂
        int brokenChainCount = analyzeBrokenChains(allLogs);
        result.setBrokenChainCount(brokenChainCount);
        
        // 分析数学错误
        int mathErrorCount = analyzeMathErrors(allLogs);
        result.setMathErrorCount(mathErrorCount);
        
        // 计算百分比
        if (allLogs.size() > 0) {
            result.setBrokenChainPercentage((double) brokenChainCount / allLogs.size() * 100);
        }
        
        log.info("数据问题分析完成: 总记录={}, 链条断裂={}, 数学错误={}", 
                allLogs.size(), brokenChainCount, mathErrorCount);
        
        return result;
    }

    /**
     * 修复 inventoryaudit 操作的数据
     * 利用正确的 afterqty 和 changeqty 计算 beforeqty
     */
    @Transactional(rollbackFor = Exception.class)
    public int fixInventoryAuditData(LocalDateTime startTime) {
        log.info("开始修复 inventoryaudit 数据");
        
        QueryWrapper<BinLocationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("create_time", startTime)
                .eq("change_type", "INVENTORY_AUDIT")
                .isNotNull("source_after_in_stock_qty")
                .isNotNull("change_qty");
        
        List<BinLocationLog> auditLogs = binLocationLogService.list(queryWrapper);
        
        int fixCount = 0;
        for (BinLocationLog log : auditLogs) {
            // 计算正确的 beforeqty = afterqty - changeqty
            Integer correctBeforeQty = log.getSourceAfterInStockQty() - log.getChangeQty();
            
            if (!correctBeforeQty.equals(log.getSourceBeforeInStockQty())) {
                log.setSourceBeforeInStockQty(correctBeforeQty);
                
                // 同位置操作，dest 数据应该与 source 一致
                if (log.getDestBinLocationDetailId() != null && 
                    log.getDestBinLocationDetailId().equals(log.getSourceBinLocationDetailId())) {
                    log.setDestBeforeInStockQty(correctBeforeQty);
                }
                
                log.setUpdateTime(LocalDateTime.now());
                log.setUpdateBy(1L);
                binLocationLogService.update(log);
                fixCount++;
            }
        }
        
        log.info("inventoryaudit 数据修复完成，修复记录数: {}", fixCount);
        return fixCount;
    }

    /**
     * 重建链条连续性
     * 确保每条记录的 beforeqty 等于前一条记录的 afterqty
     */
    @Transactional(rollbackFor = Exception.class)
    public int rebuildChainContinuity(LocalDateTime startTime) {
        log.info("开始重建链条连续性");
        
        QueryWrapper<BinLocationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("create_time", startTime)
                .isNotNull("source_bin_location_detail_id")
                .orderByAsc("source_bin_location_detail_id", "create_time");
        
        List<BinLocationLog> logs = binLocationLogService.list(queryWrapper);
        
        // 按库位详情分组
        Map<Long, List<BinLocationLog>> logsByLocationDetail = logs.stream()
                .collect(Collectors.groupingBy(BinLocationLog::getSourceBinLocationDetailId));
        
        int fixCount = 0;
        
        for (Map.Entry<Long, List<BinLocationLog>> entry : logsByLocationDetail.entrySet()) {
            List<BinLocationLog> locationLogs = entry.getValue();
            
            // 按时间排序确保顺序正确
            locationLogs.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));
            
            for (int i = 1; i < locationLogs.size(); i++) {
                BinLocationLog currentLog = locationLogs.get(i);
                BinLocationLog prevLog = locationLogs.get(i - 1);
                
                if (prevLog.getSourceAfterInStockQty() != null) {
                    // 当前记录的 beforeqty 应该等于前一条记录的 afterqty
                    if (!prevLog.getSourceAfterInStockQty().equals(currentLog.getSourceBeforeInStockQty())) {
                        currentLog.setSourceBeforeInStockQty(prevLog.getSourceAfterInStockQty());
                        
                        // 如果是同位置操作，同时更新 dest 的 beforeqty
                        if (currentLog.getDestBinLocationDetailId() != null && 
                            currentLog.getDestBinLocationDetailId().equals(currentLog.getSourceBinLocationDetailId())) {
                            currentLog.setDestBeforeInStockQty(prevLog.getSourceAfterInStockQty());
                        }
                        
                        currentLog.setUpdateTime(LocalDateTime.now());
                        currentLog.setUpdateBy(1L);
                        binLocationLogService.update(currentLog);
                        fixCount++;
                    }
                }
            }
        }
        
        log.info("链条连续性重建完成，修复记录数: {}", fixCount);
        return fixCount;
    }

    /**
     * 分析链条断裂问题
     */
    private int analyzeBrokenChains(List<BinLocationLog> logs) {
        Map<Long, List<BinLocationLog>> logsByLocationDetail = logs.stream()
                .filter(log -> log.getSourceBinLocationDetailId() != null)
                .collect(Collectors.groupingBy(BinLocationLog::getSourceBinLocationDetailId));
        
        int brokenCount = 0;
        
        for (List<BinLocationLog> locationLogs : logsByLocationDetail.values()) {
            locationLogs.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));
            
            for (int i = 1; i < locationLogs.size(); i++) {
                BinLocationLog current = locationLogs.get(i);
                BinLocationLog prev = locationLogs.get(i - 1);
                
                if (prev.getSourceAfterInStockQty() != null && 
                    current.getSourceBeforeInStockQty() != null &&
                    !prev.getSourceAfterInStockQty().equals(current.getSourceBeforeInStockQty())) {
                    brokenCount++;
                }
            }
        }
        
        return brokenCount;
    }

    /**
     * 分析数学错误
     */
    private int analyzeMathErrors(List<BinLocationLog> logs) {
        int errorCount = 0;
        
        for (BinLocationLog log : logs) {
            if (log.getSourceBeforeInStockQty() != null && 
                log.getSourceAfterInStockQty() != null && 
                log.getChangeQty() != null) {
                
                int expectedAfter = log.getSourceBeforeInStockQty() + log.getChangeQty();
                if (!log.getSourceAfterInStockQty().equals(expectedAfter)) {
                    errorCount++;
                }
            }
        }
        
        return errorCount;
    }

    // 数据类定义
    public static class DataFixResult {
        private LocalDateTime startTime;
        private LocalDateTime fixStartTime;
        private LocalDateTime fixEndTime;
        private boolean success;
        private String errorMessage;
        private DataAnalysisResult beforeFixAnalysis;
        private DataAnalysisResult afterFixAnalysis;
        private int auditFixCount;
        private int chainFixCount;
        
        // getters and setters
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getFixStartTime() { return fixStartTime; }
        public void setFixStartTime(LocalDateTime fixStartTime) { this.fixStartTime = fixStartTime; }
        public LocalDateTime getFixEndTime() { return fixEndTime; }
        public void setFixEndTime(LocalDateTime fixEndTime) { this.fixEndTime = fixEndTime; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public DataAnalysisResult getBeforeFixAnalysis() { return beforeFixAnalysis; }
        public void setBeforeFixAnalysis(DataAnalysisResult beforeFixAnalysis) { this.beforeFixAnalysis = beforeFixAnalysis; }
        public DataAnalysisResult getAfterFixAnalysis() { return afterFixAnalysis; }
        public void setAfterFixAnalysis(DataAnalysisResult afterFixAnalysis) { this.afterFixAnalysis = afterFixAnalysis; }
        public int getAuditFixCount() { return auditFixCount; }
        public void setAuditFixCount(int auditFixCount) { this.auditFixCount = auditFixCount; }
        public int getChainFixCount() { return chainFixCount; }
        public void setChainFixCount(int chainFixCount) { this.chainFixCount = chainFixCount; }
    }

    public static class DataAnalysisResult {
        private int totalRecords;
        private int brokenChainCount;
        private int mathErrorCount;
        private double brokenChainPercentage;
        
        // getters and setters
        public int getTotalRecords() { return totalRecords; }
        public void setTotalRecords(int totalRecords) { this.totalRecords = totalRecords; }
        public int getBrokenChainCount() { return brokenChainCount; }
        public void setBrokenChainCount(int brokenChainCount) { this.brokenChainCount = brokenChainCount; }
        public int getMathErrorCount() { return mathErrorCount; }
        public void setMathErrorCount(int mathErrorCount) { this.mathErrorCount = mathErrorCount; }
        public double getBrokenChainPercentage() { return brokenChainPercentage; }
        public void setBrokenChainPercentage(double brokenChainPercentage) { this.brokenChainPercentage = brokenChainPercentage; }
    }
}
