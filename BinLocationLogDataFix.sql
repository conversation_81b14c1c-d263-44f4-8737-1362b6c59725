-- BinLocationLog 数据修复脚本
-- 目标：修复错误的 beforeqty 数据，重建正确的链条连续性

-- =====================================================
-- 第一步：备份原始数据
-- =====================================================

-- 创建备份表
CREATE TABLE binlocationlog_backup_20250704 AS 
SELECT * FROM binlocationlog 
WHERE create_time >= '2024-01-01';  -- 根据实际需要调整时间范围

-- 验证备份
SELECT COUNT(*) as backup_count FROM binlocationlog_backup_20250704;

-- =====================================================
-- 第二步：分析当前数据问题
-- =====================================================

-- 查看问题数据示例
SELECT 
    id,
    ref_table_ref_num,
    change_type,
    source_bin_location_id,
    source_before_in_stock_qty,
    source_after_in_stock_qty,
    source_change_in_stock_qty,
    dest_before_in_stock_qty,
    dest_after_in_stock_qty,
    dest_change_in_stock_qty,
    create_time
FROM binlocationlog 
WHERE change_type IN ('SHIP', 'TRANSFER', 'INVENTORY_AUDIT', 'PICK', 'PUT_AWAY')
ORDER BY source_bin_location_id, create_time
LIMIT 20;

-- 检查链条断裂的情况
WITH log_chain AS (
    SELECT 
        id,
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        source_change_in_stock_qty,
        create_time,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as prev_after_qty
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    AND create_time >= '2024-01-01'  -- 根据实际需要调整
)
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) as broken_chain_count,
    ROUND(
        COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) * 100.0 / 
        COUNT(CASE WHEN prev_after_qty IS NOT NULL THEN 1 END), 2
    ) as broken_chain_percentage
FROM log_chain;

-- =====================================================
-- 第三步：修复 inventoryaudit 类型的数据
-- =====================================================

-- inventoryaudit 的 sourceafterqty, destafterqty, changeqty 是正确的
-- 需要修复 beforeqty = afterqty - changeqty

UPDATE binlocationlog 
SET 
    source_before_in_stock_qty = source_after_in_stock_qty - source_change_in_stock_qty,
    dest_before_in_stock_qty = CASE 
        WHEN dest_after_in_stock_qty IS NOT NULL AND dest_change_in_stock_qty IS NOT NULL 
        THEN dest_after_in_stock_qty - dest_change_in_stock_qty 
        ELSE NULL 
    END,
    update_time = NOW(),
    update_by = 1  -- 系统用户ID，根据实际情况调整
WHERE change_type = 'INVENTORY_AUDIT'
AND create_time >= '2024-01-01';  -- 根据实际需要调整时间范围

-- 验证 inventoryaudit 修复结果
SELECT 
    id,
    ref_table_ref_num,
    source_before_in_stock_qty,
    source_after_in_stock_qty,
    source_change_in_stock_qty,
    (source_before_in_stock_qty + source_change_in_stock_qty) as calculated_after_qty,
    CASE 
        WHEN (source_before_in_stock_qty + source_change_in_stock_qty) = source_after_in_stock_qty 
        THEN '✅ 正确' 
        ELSE '❌ 错误' 
    END as validation_result
FROM binlocationlog 
WHERE change_type = 'INVENTORY_AUDIT'
AND create_time >= '2024-01-01'
LIMIT 10;

-- =====================================================
-- 第四步：修复其他操作类型的链条连续性
-- =====================================================

-- 创建临时表存储修复后的数据
CREATE TEMPORARY TABLE binlocationlog_fixed AS
WITH ordered_logs AS (
    SELECT 
        id,
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        source_change_in_stock_qty,
        dest_bin_location_id,
        dest_before_in_stock_qty,
        dest_after_in_stock_qty,
        dest_change_in_stock_qty,
        change_type,
        create_time,
        ROW_NUMBER() OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as rn
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    AND create_time >= '2024-01-01'  -- 根据实际需要调整
    AND change_type IN ('SHIP', 'TRANSFER', 'PICK', 'PUT_AWAY')
),
chain_fixed AS (
    SELECT 
        id,
        source_bin_location_id,
        -- 第一条记录保持原样，后续记录的 beforeqty 等于前一条的 afterqty
        CASE 
            WHEN rn = 1 THEN source_before_in_stock_qty
            ELSE LAG(source_after_in_stock_qty) OVER (
                PARTITION BY source_bin_location_id 
                ORDER BY create_time
            )
        END as fixed_source_before_qty,
        source_after_in_stock_qty,
        source_change_in_stock_qty,
        dest_bin_location_id,
        -- dest 的修复逻辑类似
        CASE 
            WHEN dest_bin_location_id IS NULL THEN NULL  -- 同一库位操作
            WHEN rn = 1 THEN dest_before_in_stock_qty
            ELSE LAG(dest_after_in_stock_qty) OVER (
                PARTITION BY dest_bin_location_id 
                ORDER BY create_time
            )
        END as fixed_dest_before_qty,
        dest_after_in_stock_qty,
        dest_change_in_stock_qty,
        change_type,
        create_time,
        rn
    FROM ordered_logs
)
SELECT * FROM chain_fixed;

-- 应用修复
UPDATE binlocationlog bl
SET 
    source_before_in_stock_qty = blf.fixed_source_before_qty,
    dest_before_in_stock_qty = blf.fixed_dest_before_qty,
    update_time = NOW(),
    update_by = 1  -- 系统用户ID
FROM binlocationlog_fixed blf
WHERE bl.id = blf.id;

-- =====================================================
-- 第五步：处理同一库位操作的重复记录
-- =====================================================

-- 将同一库位操作的 dest 字段设为 NULL
UPDATE binlocationlog 
SET 
    dest_bin_location_id = NULL,
    dest_bin_location_detail_id = NULL,
    dest_before_in_stock_qty = NULL,
    dest_after_in_stock_qty = NULL,
    dest_change_in_stock_qty = NULL,
    update_time = NOW(),
    update_by = 1
WHERE source_bin_location_id = dest_bin_location_id
AND source_bin_location_detail_id = dest_bin_location_detail_id
AND create_time >= '2024-01-01';

-- =====================================================
-- 第六步：验证修复结果
-- =====================================================

-- 验证链条连续性
WITH log_chain_after_fix AS (
    SELECT 
        id,
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        source_change_in_stock_qty,
        create_time,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as prev_after_qty
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    AND create_time >= '2024-01-01'
)
SELECT 
    '修复后链条连续性检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) as broken_chain_count,
    ROUND(
        COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) * 100.0 / 
        COUNT(CASE WHEN prev_after_qty IS NOT NULL THEN 1 END), 2
    ) as broken_chain_percentage
FROM log_chain_after_fix;

-- 验证数学关系：beforeqty + changeqty = afterqty
SELECT 
    '数学关系验证' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE 
        WHEN (source_before_in_stock_qty + source_change_in_stock_qty) != source_after_in_stock_qty 
        THEN 1 
    END) as math_error_count,
    ROUND(
        COUNT(CASE 
            WHEN (source_before_in_stock_qty + source_change_in_stock_qty) != source_after_in_stock_qty 
            THEN 1 
        END) * 100.0 / COUNT(*), 2
    ) as math_error_percentage
FROM binlocationlog 
WHERE source_bin_location_id IS NOT NULL
AND create_time >= '2024-01-01';

-- 验证同一库位操作的 dest 字段清理
SELECT 
    '同一库位操作清理验证' as check_type,
    COUNT(*) as total_same_location_ops,
    COUNT(CASE WHEN dest_bin_location_id IS NOT NULL THEN 1 END) as still_has_dest_count
FROM binlocationlog 
WHERE source_bin_location_id IS NOT NULL
AND (
    source_bin_location_id = dest_bin_location_id 
    OR dest_bin_location_id IS NULL
)
AND create_time >= '2024-01-01';

-- 查看修复后的示例数据
SELECT 
    id,
    ref_table_ref_num,
    change_type,
    source_bin_location_id,
    source_before_in_stock_qty,
    source_after_in_stock_qty,
    source_change_in_stock_qty,
    dest_bin_location_id,
    dest_before_in_stock_qty,
    dest_after_in_stock_qty,
    dest_change_in_stock_qty,
    create_time
FROM binlocationlog 
WHERE source_bin_location_id IN (
    SELECT source_bin_location_id 
    FROM binlocationlog 
    WHERE create_time >= '2024-01-01' 
    GROUP BY source_bin_location_id 
    HAVING COUNT(*) > 1 
    LIMIT 3
)
ORDER BY source_bin_location_id, create_time;

-- =====================================================
-- 第七步：清理临时表
-- =====================================================

DROP TEMPORARY TABLE IF EXISTS binlocationlog_fixed;
