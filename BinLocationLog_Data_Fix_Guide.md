# BinLocationLog 数据修复指南

## 问题分析

您的数据库中存在错误的 `binlocationlog` 数据，具体情况：
- ✅ **inventoryaudit** 的 `sourceafterqty`、`destafterqty`、`changeqty` 是正确的
- ❌ **beforeqty** 字段可能不正确，导致链条断裂
- ❌ 同一库位操作可能有重复的 dest 记录

## 修复策略

### 1. 基于正确数据重建链条
由于 `inventoryaudit` 的关键字段是正确的，我们可以：
- 使用 `afterqty - changeqty = beforeqty` 的公式修复 `inventoryaudit` 记录
- 基于时间顺序重建其他操作的链条连续性

### 2. 分步骤修复
1. **备份原始数据**
2. **修复 inventoryaudit 类型**
3. **重建链条连续性**
4. **清理重复记录**
5. **验证修复结果**

## 执行步骤

### 步骤 1：数据备份和分析

```sql
-- 1.1 创建备份表
CREATE TABLE binlocationlog_backup_20250704 AS 
SELECT * FROM binlocationlog 
WHERE create_time >= '2024-01-01';  -- 根据实际需要调整时间范围

-- 1.2 分析当前问题
WITH log_chain AS (
    SELECT 
        id,
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        create_time,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as prev_after_qty
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
)
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) as broken_chain_count
FROM log_chain;
```

### 步骤 2：修复 inventoryaudit 数据

```sql
-- 2.1 修复 inventoryaudit 的 beforeqty
UPDATE binlocationlog 
SET 
    source_before_in_stock_qty = source_after_in_stock_qty - source_change_in_stock_qty,
    dest_before_in_stock_qty = CASE 
        WHEN dest_after_in_stock_qty IS NOT NULL AND dest_change_in_stock_qty IS NOT NULL 
        THEN dest_after_in_stock_qty - dest_change_in_stock_qty 
        ELSE NULL 
    END,
    update_time = NOW()
WHERE change_type = 'INVENTORY_AUDIT';

-- 2.2 验证修复结果
SELECT 
    COUNT(*) as total_audit_records,
    COUNT(CASE 
        WHEN (source_before_in_stock_qty + source_change_in_stock_qty) != source_after_in_stock_qty 
        THEN 1 
    END) as math_error_count
FROM binlocationlog 
WHERE change_type = 'INVENTORY_AUDIT';
```

### 步骤 3：重建链条连续性

```sql
-- 3.1 创建修复逻辑的临时表
CREATE TEMPORARY TABLE binlocationlog_chain_fix AS
WITH ordered_logs AS (
    SELECT 
        id,
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        source_change_in_stock_qty,
        create_time,
        ROW_NUMBER() OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as rn
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    AND change_type IN ('SHIP', 'TRANSFER', 'PICK', 'PUT_AWAY')
)
SELECT 
    id,
    source_bin_location_id,
    CASE 
        WHEN rn = 1 THEN source_before_in_stock_qty  -- 第一条记录保持原样
        ELSE LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        )
    END as fixed_source_before_qty
FROM ordered_logs;

-- 3.2 应用修复
UPDATE binlocationlog bl
SET 
    source_before_in_stock_qty = blcf.fixed_source_before_qty,
    update_time = NOW()
FROM binlocationlog_chain_fix blcf
WHERE bl.id = blcf.id;
```

### 步骤 4：清理重复记录

```sql
-- 4.1 将同一库位操作的 dest 字段设为 NULL
UPDATE binlocationlog 
SET 
    dest_bin_location_id = NULL,
    dest_bin_location_detail_id = NULL,
    dest_before_in_stock_qty = NULL,
    dest_after_in_stock_qty = NULL,
    dest_change_in_stock_qty = NULL,
    update_time = NOW()
WHERE source_bin_location_id = dest_bin_location_id
AND source_bin_location_detail_id = dest_bin_location_detail_id;
```

### 步骤 5：验证修复结果

```sql
-- 5.1 验证链条连续性
WITH log_chain_after_fix AS (
    SELECT 
        source_bin_location_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        create_time,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_id 
            ORDER BY create_time
        ) as prev_after_qty
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
)
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) as broken_chain_count,
    ROUND(
        COUNT(CASE WHEN prev_after_qty IS NOT NULL AND source_before_in_stock_qty != prev_after_qty THEN 1 END) * 100.0 / 
        COUNT(CASE WHEN prev_after_qty IS NOT NULL THEN 1 END), 2
    ) as broken_chain_percentage
FROM log_chain_after_fix;

-- 5.2 验证数学关系
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE 
        WHEN (source_before_in_stock_qty + source_change_in_stock_qty) != source_after_in_stock_qty 
        THEN 1 
    END) as math_error_count
FROM binlocationlog 
WHERE source_bin_location_id IS NOT NULL;
```

## 特殊情况处理

### 1. 如果第一条记录的 beforeqty 也不正确

```sql
-- 查找每个库位的第一条记录
WITH first_records AS (
    SELECT 
        source_bin_location_id,
        MIN(create_time) as first_time
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    GROUP BY source_bin_location_id
)
SELECT 
    bl.id,
    bl.source_bin_location_id,
    bl.source_before_in_stock_qty,
    bl.source_after_in_stock_qty,
    bl.source_change_in_stock_qty,
    -- 如果第一条记录的数学关系不对，需要手动检查
    (bl.source_before_in_stock_qty + bl.source_change_in_stock_qty) as calculated_after_qty
FROM binlocationlog bl
JOIN first_records fr ON bl.source_bin_location_id = fr.source_bin_location_id 
    AND bl.create_time = fr.first_time
WHERE (bl.source_before_in_stock_qty + bl.source_change_in_stock_qty) != bl.source_after_in_stock_qty;
```

### 2. 如果需要基于实际库存重建

```sql
-- 如果有当前实际库存数据，可以从最新记录往前推算
WITH latest_records AS (
    SELECT 
        source_bin_location_id,
        source_after_in_stock_qty as current_stock,
        MAX(create_time) as latest_time
    FROM binlocationlog 
    WHERE source_bin_location_id IS NOT NULL
    GROUP BY source_bin_location_id
),
reverse_calculation AS (
    SELECT 
        bl.id,
        bl.source_bin_location_id,
        bl.source_change_in_stock_qty,
        bl.create_time,
        -- 从最新库存往前推算
        lr.current_stock - SUM(bl2.source_change_in_stock_qty) OVER (
            PARTITION BY bl.source_bin_location_id 
            ORDER BY bl.create_time DESC 
            ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING
        ) + bl.source_change_in_stock_qty as calculated_before_qty
    FROM binlocationlog bl
    JOIN latest_records lr ON bl.source_bin_location_id = lr.source_bin_location_id
    LEFT JOIN binlocationlog bl2 ON bl2.source_bin_location_id = bl.source_bin_location_id 
        AND bl2.create_time > bl.create_time
    WHERE bl.source_bin_location_id IS NOT NULL
)
-- 这个查询可以帮助验证计算是否正确
SELECT * FROM reverse_calculation LIMIT 10;
```

## 注意事项

1. **执行前务必备份数据**
2. **分批执行**：如果数据量很大，建议分批处理
3. **验证每个步骤**：每步执行后都要验证结果
4. **业务影响**：在业务低峰期执行
5. **回滚准备**：准备回滚方案

## 执行顺序

1. ✅ 备份数据
2. ✅ 分析问题规模
3. ✅ 修复 inventoryaudit
4. ✅ 重建链条连续性
5. ✅ 清理重复记录
6. ✅ 全面验证
7. ✅ 清理临时表

## 验证成功标准

- 链条断裂率 < 1%
- 数学关系错误率 = 0%
- 同一库位操作的 dest 字段已清理
- 所有 inventoryaudit 记录的数学关系正确

执行完成后，您的 `binlocationlog` 数据将形成正确的连续链条，每条记录的 `afterqty` 都能与下一条记录的 `beforeqty` 正确衔接。
