-- =====================================================
-- BinLocationLog 数据修复 SQL 脚本（修正版）
-- =====================================================

-- 修复目标：
-- 1. 修复 inventoryaudit 操作的 beforeqty 数据准确性
-- 2. 重建链条连续性，确保 afterQty(n) = beforeQty(n+1)
-- 3. 保持同位置操作的完整 source 和 dest 信息

-- =====================================================
-- 第一步：备份原始数据
-- =====================================================

-- 创建备份表
CREATE TABLE binlocationlog_backup_20250107 AS 
SELECT * FROM binlocationlog 
WHERE create_time >= '2024-01-01';

-- 验证备份
SELECT COUNT(*) as backup_count FROM binlocationlog_backup_20250107;

-- =====================================================
-- 第二步：数据问题分析
-- =====================================================

-- 分析链条断裂情况
WITH chain_analysis AS (
    SELECT 
        bl1.id as current_id,
        bl1.source_bin_location_detail_id,
        bl1.source_before_in_stock_qty as current_before,
        bl1.source_after_in_stock_qty as current_after,
        LAG(bl1.source_after_in_stock_qty) OVER (
            PARTITION BY bl1.source_bin_location_detail_id 
            ORDER BY bl1.create_time
        ) as prev_after,
        bl1.create_time
    FROM binlocationlog bl1
    WHERE bl1.create_time >= '2024-01-01'
    AND bl1.source_bin_location_detail_id IS NOT NULL
)
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after IS NOT NULL AND current_before != prev_after THEN 1 END) as broken_chains,
    ROUND(
        COUNT(CASE WHEN prev_after IS NOT NULL AND current_before != prev_after THEN 1 END) * 100.0 / 
        COUNT(CASE WHEN prev_after IS NOT NULL THEN 1 END), 2
    ) as broken_chain_percentage
FROM chain_analysis;

-- 分析数学错误（beforeqty + changeqty != afterqty）
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN source_before_in_stock_qty + change_qty != source_after_in_stock_qty THEN 1 END) as math_errors,
    ROUND(
        COUNT(CASE WHEN source_before_in_stock_qty + change_qty != source_after_in_stock_qty THEN 1 END) * 100.0 / 
        COUNT(*), 2
    ) as math_error_percentage
FROM binlocationlog 
WHERE create_time >= '2024-01-01'
AND source_before_in_stock_qty IS NOT NULL
AND source_after_in_stock_qty IS NOT NULL
AND change_qty IS NOT NULL;

-- =====================================================
-- 第三步：修复 inventoryaudit 数据
-- =====================================================

-- 修复 inventoryaudit 操作的 beforeqty
-- 使用公式：beforeqty = afterqty - changeqty
UPDATE binlocationlog 
SET 
    source_before_in_stock_qty = source_after_in_stock_qty - change_qty,
    -- 同位置操作时，同时更新 dest 的 beforeqty
    dest_before_in_stock_qty = CASE 
        WHEN dest_bin_location_detail_id = source_bin_location_detail_id 
        THEN source_after_in_stock_qty - change_qty 
        ELSE dest_before_in_stock_qty 
    END,
    update_time = NOW(),
    update_by = 1
WHERE create_time >= '2024-01-01'
AND change_type = 'INVENTORY_AUDIT'
AND source_after_in_stock_qty IS NOT NULL
AND change_qty IS NOT NULL
AND (source_before_in_stock_qty != source_after_in_stock_qty - change_qty 
     OR source_before_in_stock_qty IS NULL);

-- 验证 inventoryaudit 修复结果
SELECT 
    COUNT(*) as total_audit_records,
    COUNT(CASE WHEN source_before_in_stock_qty + change_qty = source_after_in_stock_qty THEN 1 END) as correct_math,
    COUNT(CASE WHEN source_before_in_stock_qty + change_qty != source_after_in_stock_qty THEN 1 END) as math_errors
FROM binlocationlog 
WHERE create_time >= '2024-01-01'
AND change_type = 'INVENTORY_AUDIT'
AND source_before_in_stock_qty IS NOT NULL
AND source_after_in_stock_qty IS NOT NULL
AND change_qty IS NOT NULL;

-- =====================================================
-- 第四步：重建链条连续性
-- =====================================================

-- 创建临时表存储修复计划
CREATE TEMPORARY TABLE chain_fix_plan AS
WITH ordered_logs AS (
    SELECT 
        id,
        source_bin_location_detail_id,
        source_before_in_stock_qty,
        source_after_in_stock_qty,
        dest_bin_location_detail_id,
        dest_before_in_stock_qty,
        dest_after_in_stock_qty,
        create_time,
        LAG(source_after_in_stock_qty) OVER (
            PARTITION BY source_bin_location_detail_id 
            ORDER BY create_time
        ) as correct_before_qty,
        ROW_NUMBER() OVER (
            PARTITION BY source_bin_location_detail_id 
            ORDER BY create_time
        ) as row_num
    FROM binlocationlog
    WHERE create_time >= '2024-01-01'
    AND source_bin_location_detail_id IS NOT NULL
    AND change_type IN ('SHIP', 'TRANSFER', 'PICK', 'PUT_AWAY')
)
SELECT 
    id,
    source_bin_location_detail_id,
    dest_bin_location_detail_id,
    correct_before_qty,
    source_before_in_stock_qty as current_before_qty
FROM ordered_logs
WHERE row_num > 1  -- 跳过第一条记录
AND correct_before_qty IS NOT NULL
AND correct_before_qty != source_before_in_stock_qty;

-- 执行链条修复
UPDATE binlocationlog bl
JOIN chain_fix_plan cfp ON bl.id = cfp.id
SET 
    bl.source_before_in_stock_qty = cfp.correct_before_qty,
    -- 如果是同位置操作，同时更新 dest 的 beforeqty
    bl.dest_before_in_stock_qty = CASE 
        WHEN bl.dest_bin_location_detail_id = bl.source_bin_location_detail_id 
        THEN cfp.correct_before_qty 
        ELSE bl.dest_before_in_stock_qty 
    END,
    bl.update_time = NOW(),
    bl.update_by = 1;

-- 获取修复统计
SELECT COUNT(*) as chain_fix_count FROM chain_fix_plan;

-- 清理临时表
DROP TEMPORARY TABLE chain_fix_plan;

-- =====================================================
-- 第五步：验证修复结果
-- =====================================================

-- 重新检查链条断裂情况
WITH chain_analysis_after AS (
    SELECT 
        bl1.id as current_id,
        bl1.source_bin_location_detail_id,
        bl1.source_before_in_stock_qty as current_before,
        bl1.source_after_in_stock_qty as current_after,
        LAG(bl1.source_after_in_stock_qty) OVER (
            PARTITION BY bl1.source_bin_location_detail_id 
            ORDER BY bl1.create_time
        ) as prev_after,
        bl1.create_time
    FROM binlocationlog bl1
    WHERE bl1.create_time >= '2024-01-01'
    AND bl1.source_bin_location_detail_id IS NOT NULL
)
SELECT 
    '修复后' as status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN prev_after IS NOT NULL AND current_before != prev_after THEN 1 END) as broken_chains,
    ROUND(
        COUNT(CASE WHEN prev_after IS NOT NULL AND current_before != prev_after THEN 1 END) * 100.0 / 
        COUNT(CASE WHEN prev_after IS NOT NULL THEN 1 END), 2
    ) as broken_chain_percentage
FROM chain_analysis_after;

-- 重新检查数学错误
SELECT 
    '修复后' as status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN source_before_in_stock_qty + change_qty != source_after_in_stock_qty THEN 1 END) as math_errors,
    ROUND(
        COUNT(CASE WHEN source_before_in_stock_qty + change_qty != source_after_in_stock_qty THEN 1 END) * 100.0 / 
        COUNT(*), 2
    ) as math_error_percentage
FROM binlocationlog 
WHERE create_time >= '2024-01-01'
AND source_before_in_stock_qty IS NOT NULL
AND source_after_in_stock_qty IS NOT NULL
AND change_qty IS NOT NULL;

-- =====================================================
-- 第六步：修复结果汇总
-- =====================================================

-- 显示修复前后对比
SELECT 
    'inventoryaudit修复' as fix_type,
    COUNT(*) as affected_records
FROM binlocationlog 
WHERE create_time >= '2024-01-01'
AND change_type = 'INVENTORY_AUDIT'
AND update_time >= CURDATE();

-- 验证修复成功的标准
-- 1. 链条断裂数量 = 0
-- 2. 数学错误数量 = 0
-- 3. 所有 inventoryaudit 记录的数学关系正确

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM (
                WITH chain_check AS (
                    SELECT 
                        LAG(source_after_in_stock_qty) OVER (
                            PARTITION BY source_bin_location_detail_id 
                            ORDER BY create_time
                        ) as prev_after,
                        source_before_in_stock_qty as current_before
                    FROM binlocationlog
                    WHERE create_time >= '2024-01-01'
                    AND source_bin_location_detail_id IS NOT NULL
                )
                SELECT 1 FROM chain_check 
                WHERE prev_after IS NOT NULL AND current_before != prev_after
            ) t
        ) = 0 
        AND (
            SELECT COUNT(*) FROM binlocationlog 
            WHERE create_time >= '2024-01-01'
            AND source_before_in_stock_qty IS NOT NULL
            AND source_after_in_stock_qty IS NOT NULL
            AND change_qty IS NOT NULL
            AND source_before_in_stock_qty + change_qty != source_after_in_stock_qty
        ) = 0
        THEN '✅ 修复成功：链条连续，数学关系正确'
        ELSE '❌ 修复未完成：仍存在问题'
    END as fix_result;
